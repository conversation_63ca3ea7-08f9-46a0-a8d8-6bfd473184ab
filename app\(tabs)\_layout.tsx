import tw from '@root/tailwind';
import {icons} from '@src/constants';
import {useAppSelector} from '@src/redux/hooks';
import {Tabs, useNavigation} from 'expo-router';
import {startCase} from 'lodash';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';

export default function () {
  const navigation = useNavigation();
  const {t} = useTranslation();
  const {isRTL} = useAppSelector(state => state.locale);
  return (
    <Tabs
      initialRouteName={'home'}
      screenOptions={({route, navigation}) => {
        const state = navigation.getState();
        const hasNestedNavigation =
          state.routes[state.index]?.state?.routes[0]?.state?.routes?.length >
          1;
        return {
          headerShown: false,
          lazy: true,
          tabBarStyle: tw.style(
            `${
              !hasNestedNavigation
                ? 'flex justify-center items-center'
                : 'hidden'
            } bg-white/90 absolute ios:p-4 android:p-4  android:border ios:border border-gray-300 android:bottom-2 android:bottom-8 ios:bottom-10   android:mx-4 ios:mx-6  rounded-full android:h-20`,
          ),
          tabBarItemStyle: tw`${
            isRTL ? `border-l` : `border-r`
          } border-gray-50 last:border-0`,
          tabBarLabelStyle: tw`text-md android:top-2 font-alfont text-bold`,
          tabBarActiveTintColor: tw.color(`theme-700`),
          tabBarInactiveTintColor: tw.color(`gray-400`),
        };
      }}>
      <Tabs.Screen
        name="(drawer)"
        options={{
          title: startCase(`${t('home')}`),
          tabBarIcon: ({color, focused}) => (
            <icons.home
              color={focused ? tw.color(`theme-700`) : tw.color(`gray-400`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
          ),
          headerStyle: tw`bg-gray-200`,
          headerTransparent: true,
          headerTitle: () => <View style={tw`bg-gray-200 `}></View>,
          headerRight: () => (
            <></>
            // <View style={tw`pt-[45%] flex-col m-3`}>
            //   <View style={tw`bg-white rounded-full p-3 `}>
            //     <icons.account color={'#899499'} width={25} height={25} />
            //   </View>
            //   <View style={tw`bg-white rounded-full self-center p-2 top-3 `}>
            //     <icons.homeSettings color={'#899499'}  width={35} height={35} />
            //   </View>
            // </View>
          ),
          headerLeft: () => <View style={tw``}></View>,
        }}
      />
      <Tabs.Screen
        name="Services"
        options={{
          headerShown: true,
          title: startCase(`${t('services')}`),
          tabBarIcon: ({color, focused}) => (
            <icons.services
              color={focused ? tw.color(`theme-700`) : tw.color(`gray-400`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
          ),
          header: (props: any) => {
            return (
              <View
                style={tw`flex flex-row justify-start items-center w-full  rounded-xl  h-14 mx-6`}>
                <Text
                  style={tw`capitalize font-alfont  truncate font-alfont text-lg pt-1`}>
                  {props.route?.params?.name ??
                    props.options.headerTitle ??
                    props.options.title}
                </Text>
              </View>
            );
          },
        }}
      />
      <Tabs.Screen
        name="Courses"
        options={{
          title: startCase(`${t('courses')}`),
          headerShown: false,
          tabBarIcon: ({color, focused}) => (
            <icons.courses
              color={focused ? tw.color(`theme-700`) : tw.color(`gray-400`)}
              style={tw` ${isRTL && `rotate-180`} h-5 w-5`}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="Profile"
        options={{
          title: startCase(`${t('profile')}`),
          headerShown: false,
          tabBarIcon: ({color, focused}) => (
            <icons.account
              color={focused ? tw.color(`theme-700`) : tw.color(`gray-400`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
          ),
        }}
      />
    </Tabs>
  );
}
