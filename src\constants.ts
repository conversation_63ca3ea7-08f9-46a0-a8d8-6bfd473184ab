import Attendance from '@root/assets/icons/Attendance.svg';
import CourseShowBookIcon from '@root/assets/icons/CourseShowBookIcon.svg';
import Date from '@root/assets/icons/Date.svg';
import Forget_Password from '@root/assets/icons/Forget_Password.svg';
import Hall from '@root/assets/icons/Hall.svg';
import LogoAr from '@root/assets/icons/LogoAr.svg';
import LogoEn from '@root/assets/icons/LogoEn.svg';
import New_Password from '@root/assets/icons/New_Password.svg';
import Session from '@root/assets/icons/Session.svg';
import Sidebar from '@root/assets/icons/Sidebar.svg';
import ArrowLeft from '@root/assets/icons/arrowleft.svg';
import bankHall from '@root/assets/icons/bankhall.svg';
import books from '@root/assets/icons/books.svg';
import calendar from '@root/assets/icons/calendar.svg';
import Courses from '@root/assets/icons/courses.svg';
import download from '@root/assets/icons/download.svg';
import grade from '@root/assets/icons/grade.svg';
import HomeIcon from '@root/assets/icons/home.svg';
import icon from '@root/assets/icons/icon.svg';
import instructor from '@root/assets/icons/instructor.svg';
import login_logo from '@root/assets/icons/login_logo.svg';
import notes from '@root/assets/icons/notes.svg';
import online from '@root/assets/icons/online.svg';
import sectionIcon from '@root/assets/icons/sectionIcon.svg';
import Services from '@root/assets/icons/services.svg';
import sessionIcon from '@root/assets/icons/sessionIcon.svg';
import sidebar_Consultancy from '@root/assets/icons/sidebar_Consultancy.svg';
import sidebar_contactUs from '@root/assets/icons/sidebar_ContactUs.svg';
import sideBarCourses from '@root/assets/icons/sidebar_Courses.svg';
import sidebar_volunteers from '@root/assets/icons/sidebar_Volunteer.svg';
import sidebar_changePassword from '@root/assets/icons/sidebar_changePassword.svg';
import sidebar_forms from '@root/assets/icons/sidebar_forms.svg';
import sidebar_invoices from '@root/assets/icons/sidebar_invoices.svg';
import subject from '@root/assets/icons/subject.svg';
import TabSettings from '@root/assets/icons/tabsSettings.svg';
import teacher from '@root/assets/icons/teacher.svg';
import Account from '@root/assets/icons/user.svg';
import video from '@root/assets/icons/video.svg';
import DeleteAccount from '@root/assets/icons/delete.svg';
import moment from 'moment';
import { useState } from 'react';

export const appName = 'StriveEdu';
export const appSchema = `strive://`;
export const striveEdu = appName === 'StriveEdu';
export const isLocal = __DEV__;
export const appVersion = `0.0.1`;
export const keepCache: number = __DEV__ ? 0 : 0;
// baseUrl + x-api-key + appName + appSchema +
export const baseUrl = isLocal
  ? // ? `http://strive-backend.test`
  process.env.EXPO_PUBLIC_API_URL_LOCAL
  : process.env.EXPO_PUBLIC_API_URL_PROD;
export const apiUrl = `${baseUrl}/api/`;

export const getImage = (name?: string) => {
  return `${baseUrl}/images/${name}`;
};

export const storageUrl = `${baseUrl}storage/uploads/`;

export const images = {
  logo: require('../assets/images/strive_logo.png'),
  join_us: require('../assets/images/join_us.png'),
  book: require('../assets/images/book.png'),
  no_results: require('../assets/images/no_results.png'),
  bg: require('../assets/images/bg.jpg'),
  loader: require('../assets/images/loader.json'),
  failed: require('../assets/images/failed_1.png'),
  failed_2: require('../assets/images/failed_2.png'),
  payment_success: require('../assets/images/payment_success.png'),
};

export const Colors = {
  inActiveDotStyleColor: '#ADDAD5',
  activeDotColor: '#45b4a0',
};

export const icons = {
  home: HomeIcon,
  icon: icon,
  services: Services,
  courses: Courses,
  account: Account,
  logoEn: LogoEn,
  logoAr: LogoAr,
  homeSettings: TabSettings,
  Attendance: Attendance,
  Date: Date,
  Hall: Hall,
  Session: Session,
  Sidebar: Sidebar,
  ArrowLeft: ArrowLeft,
  sideBarCourses: sideBarCourses,
  sidebar_Consultancy: sidebar_Consultancy,
  sidebar_forms: sidebar_forms,
  sidebar_invoices: sidebar_invoices,
  sidebar_changePassword: sidebar_changePassword,
  sidebar_volunteers: sidebar_volunteers,
  sidebar_contactUs: sidebar_contactUs,
  login_logo: login_logo,
  Forget_Password: Forget_Password,
  New_Password: New_Password,
  CourseShowBookIcon: CourseShowBookIcon,
  online: online,
  sessionIcon: sessionIcon,
  bankHall: bankHall,
  grade: grade,
  subject: subject,
  teacher: teacher,
  sectionIcon: sectionIcon,
  notes: notes,
  video: video,
  download: download,
  calendar: calendar,
  instructor: instructor,
  books: books,
  deleteAccount: DeleteAccount
};

export const imageSizes: {
  xxxs: number;
  xxs: number;
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
} = {
  xxxs: 10,
  xxs: 15,
  xs: 20,
  sm: 25,
  md: 30,
  lg: 35,
  xl: 200,
};

export const getCourseGroupColor = (status: string) => {
  switch (status) {
    case 'canceled':
      return 'red';

    case 'recorded':
      return 'green';
    default:
      return 'gray';
  }
};

export const getLocaleDate = (date: string | Date, isRTL: boolean) =>
  isRTL ? moment(date).format('YYYY/MM/DD') : moment(date).format('DD/MM/YYYY');

export const searchModules: any = [
  {
    id: 1,
    name: 'students',
    type: 'user',
    query: { role: 'student' },
    showFront: false,
  },
  {
    id: 2,
    name: 'teachers',
    type: 'user',
    query: { role: 'teacher' },
    showFront: false,
  },
  {
    id: 3,
    name: 'subjects',
    type: 'subject',
    query: undefined,
    showFront: false,
  },
  {
    id: 4,
    name: 'online_courses',
    type: 'course',
    query: { group: 'online' },
    showFront: true,
  },
  {
    id: 5,
    name: 'attendance_courses',
    type: 'course',
    query: { group: 'attendance' },
    showFront: true,
  },
  {
    id: 6,
    name: 'recorded_courses',
    type: 'course',
    query: { group: 'recorded' },
    showFront: true,
  },
];

type FormProps = {
  standardMarks: {
    high_school_percentage: number;
    subject_1: number;
    subject_2: number;
    subject_3: number;
    subject_4: number;
  };
  studentMarks: {
    high_school_percentage: number;
    subject_1: number;
    subject_2: number;
    subject_3: number;
    subject_4: number;
  };
  resultMarks: {
    high_school_percentage: number;
    subject_1: number;
    subject_2: number;
    subject_3: number;
    subject_4: number;
  };
  rate: number;
};

// Define the initial values

type UseFormReturn<T> = {
  dataState: T;
  setData: (key: keyof T | Partial<T>, value?: any) => void;
  reset: () => void;
};

function useForm<T>(initialValues: T): UseFormReturn<T> {
  const [dataState, setDataState] = useState<T>(initialValues);

  const setData = (key: keyof T | Partial<T>, value?: any) => {
    setDataState(prevData => {
      if (typeof key === 'object' && value === undefined) {
        // Handle multiple values
        return {
          ...prevData,
          ...key,
        };
      } else {
        // Handle single value
        return {
          ...prevData,
          [key as keyof T]:
            value !== undefined
              ? {
                ...prevData[key as keyof T],
                ...value,
              }
              : prevData[key as keyof T], // Maintain existing value if no new value provided
        };
      }
    });
  };

  const reset = () => {
    setDataState(initialValues);
  };

  return {
    dataState,
    setData,
    reset,
  };
}

export { useForm };

export const appRoutes = (params?: string | number) => {
  return {
    home: `(tabs)/(drawer)/home/<USER>
    services: `(tabs)/Services`,
    cart: `(tabs)/(drawer)/home/<USER>/`,
    payment: `(tabs)/(drawer)/home/<USER>/payment`,
    paymentSuccess: `(tabs)/(drawer)/home/<USER>/paymentSuccess`,
    profile: `(tabs)/Profile`,
    profileUpdate: `(tabs)/(drawer)/home/<USER>/`,
    profileCourses: `(tabs)/(drawer)/home/<USER>/courses`,
    profileDocuments: `(tabs)/(drawer)/home/<USER>/documents`,
    profileQuestionnaires: `(tabs)/(drawer)/home/<USER>/questionnaires`,
    profileInvoices: `(tabs)/(drawer)/home/<USER>/invoice/`,
    profileInvoice: `(tabs)/(drawer)/home/<USER>/invoice/${params}`,
    courseIndex: `(tabs)/(drawer)/home/<USER>
    courseShow: `(tabs)/(drawer)/home/<USER>/${params}`,
    userIndex: `(tabs)/(drawer)/home/<USER>
    userShow: `(tabs)/(drawer)/home/<USER>/${params}`,
    documentIndex: `(tabs)/(drawer)/home/<USER>
    documentShow: `(tabs)/(drawer)/home/<USER>/${params}`,
    questionnaireIndex: `(tabs)/(drawer)/home/<USER>
    questionnaireShow: `(tabs)/(drawer)/home/<USER>/${params}`,
    contactus: `(tabs)/(drawer)/home/<USER>
    aboutus: `(tabs)/(drawer)/home/<USER>
    login: `(tabs)/(drawer)/home/<USER>
    register: `(tabs)/(drawer)/home/<USER>
    change_password: `(tabs)/(drawer)/home/<USER>/change_Password`,
    forgot_password: `(tabs)/(drawer)/home/<USER>/forgot_password`,
  };
};

export const whatsappUrl = (phone: string | number, message?: string) =>
  `https://api.whatsapp.com/send?phone=${phone}&${message ? `text=${message}` : ``
  }`;
