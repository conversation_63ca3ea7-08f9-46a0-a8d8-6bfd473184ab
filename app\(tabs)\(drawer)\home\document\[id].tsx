import {Feather, FontAwesome5, SimpleLineIcons} from '@expo/vector-icons';
import DocumentCard from '@root/src/components/document/DocumentCard';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import TagCard from '@root/src/components/tag/TagCard';
import {appRoutes, icons} from '@root/src/constants';
import {useLazyGetMyDocumentsQuery} from '@root/src/redux/api/authApi';
import {
  useGetDocQuery,
  useGetDocWithTagQuery,
} from '@root/src/redux/api/documentApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {isAuthenticated} from '@root/src/redux/slices/authSlice';
import {
  resetCart,
  setCartElements,
  setCartKind,
  setElement,
  setStudent,
} from '@root/src/redux/slices/cartSlice';
import {
  showErrorToastMessage,
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {AppQueryResult, Doc} from '@root/src/types';
import tw from '@root/tailwind';
import {useLocalSearchParams, useRouter} from 'expo-router';
import {includes, map} from 'lodash';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FlatList,
  RefreshControl,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const {id}: {id?: string} = useLocalSearchParams();
  const params = useLocalSearchParams();
  const {navigate, push} = useRouter();
  const isAuth = useAppSelector(isAuthenticated);
  const [triggerGetMyDocuments] = useLazyGetMyDocumentsQuery();
  const {
    auth,
    cart: {total},
    locale: {isRTL},
  } = useAppSelector(state => state);
  const dispatch = useAppDispatch();

  const {
    data: element,
    isFetching,
    isSuccess,
    refetch,
  } = useGetDocQuery<{
    data: Doc;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(id);

  const {
    data: elements,
    isFetching: relatedDocsFetching,
    isSuccess: relatedDocsSuccess,
    refetch: relatedDocsRefetch,
  } = useGetDocWithTagQuery<{
    data: AppQueryResult<Doc[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>({tagId: id ?? ''}); // or { id: id ?? 0 }

  useEffect(() => {
    if (total > 0 && isSuccess) {
      dispatch(resetCart());
    }
  }, []);

  const handleAddToCart = async () => {
    if (!isAuth || element.price <= 0) {
      return dispatch(
        showErrorToastMessage({content: t('u_r_not_authenticated')}),
      );
    }
    dispatch(setCartKind('document'));
    const isPurchased = await triggerGetMyDocuments().then((r: any) => {
      if (r && r?.data) {
        const ids = map(r.data.data, 'id');
        return includes(ids, element.id);
      } else {
        return false;
      }
    });
    if (isPurchased) {
      dispatch(
        showWarningToastMessage({
          content: 'element_is_already_purchased_before',
        }),
      );
      push(appRoutes(``).profileDocuments);
    } else {
      dispatch(setElement(element));
      dispatch(
        setStudent({
          email: auth.email,
          mobile: auth.mobile,
          name: auth.name,
        }),
      );
      dispatch(setCartElements([element]));
      dispatch(
        showSuccessToastMessage({
          content: t('element_added_to_ur_cart_sucessfully'),
        }),
      );
      navigate(appRoutes().cart);
    }
  };

  if (isFetching) return <MainLoadingView />;

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-grow mx-auto w-full`}
      contentInset={{bottom: 100}}
      style={tw`rounded-xl m-3`}>
      <View style={tw`bg-white m-2  rounded-3xl p-3 gap-x-2`}>
        <View style={tw`flex-row justify-between items-center`}>
          <View style={tw`flex-row justify-start items-center gap-x-2 p-2 `}>
            {/* {Icon & Desc} */}
            <icons.books color={tw.color(``)} style={tw`h-10 w-10`} />
            <Text
              style={tw`font-alfont capitalize text-black font-bold text-left`}>
              {element.name}
            </Text>
          </View>
          {/* {price} */}
          {element.price > 0 ? (
            <View style={tw`flex flex-row px-2`}>
              <Text
                style={tw`text-xl text-theme-800 font-alfont capitalize`}>{`${
                element.price
              } ${t('kd')}`}</Text>
            </View>
          ) : (
            <Text style={tw`text-md text-theme-800  font-alfont capitalize`}>
              {t('free')}
            </Text>
          )}
        </View>
        <View style={tw`gap-x-2 p-3`}>
          <Text style={tw`body-text font-alfont text-gray-500 leading-loose`}>
            {element.description}
          </Text>
        </View>

        <View style={tw`p-2`}>
          {/* icons */}
          {element.subject && (
            <>
              <View
                style={tw`flex-row pt-3 justify-start items-center gap-x-2 `}>
                <TouchableOpacity
                  style={tw`h-8 w-8 flex justify-center items-center p-1.2 rounded-md border border-gray-300`}>
                  <FontAwesome5
                    size={18}
                    color={tw.color(`gray-500`)}
                    style={tw``}
                    name={isRTL ? 'book' : 'book'}
                  />
                </TouchableOpacity>

                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {element.subject?.name}
                </Text>
              </View>
              <View
                style={tw`flex-row pt-3 justify-start items-center gap-x-2`}>
                <TouchableOpacity
                  style={tw`h-8 w-8 flex justify-center items-center p-1.2 rounded-md border border-gray-300`}>
                  <Feather
                    size={18}
                    color={tw.color(`gray-500`)}
                    style={tw``}
                    name={isRTL ? 'clipboard' : 'clipboard'}
                  />
                </TouchableOpacity>
                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {element.subject?.grade?.name}
                </Text>
              </View>

              <View
                style={tw`flex-row pt-3 justify-start items-center gap-x-2 `}>
                <TouchableOpacity
                  style={tw`h-8 w-8 flex justify-center items-center p-1.2 rounded-md border border-gray-300`}>
                  <SimpleLineIcons
                    size={18}
                    color={tw.color(`gray-500`)}
                    style={tw``}
                    name={isRTL ? 'graduation' : 'graduation'}
                  />
                </TouchableOpacity>
                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {element.subject?.grade?.stage?.name}
                </Text>
              </View>
            </>
          )}

          {/* Tags */}

          <View style={tw`flex-row justify-start items-center mt-[6%] gap-x-2`}>
            {element.tags &&
              map(element.tags, (t, i) => (
                <TagCard element={t} type="document" />
              ))}
          </View>
          <TouchableOpacity
            onPress={() => handleAddToCart()}
            style={tw`bg-white p-4 mt-[10%] rounded-3xl border border-gray-200 flex justify-center items-center`}>
            <Text style={tw`font-alfont text-theme-700 text-md`}>
              {t('purchase_book')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      <View>
        <View style={tw`bg-white mt-[3%] rounded-3xl p-3 hidden`}>
          <Text
            style={tw`font-alfont text-lg capitalize text-black font-bold p-2 text-left`}>
            {t('related_documents')}
          </Text>

          <FlatList
            refreshControl={
              <RefreshControl
                refreshing={isFetching}
                onRefresh={refetch}
                tintColor={tw.color(`theme-700`)}
              />
            }
            ListHeaderComponentStyle={tw`flex flex-1 w-full h-auto`}
            style={tw`mx-3 rounded-xl`}
            horizontal={true}
            contentContainerStyle={tw`flex justify-center w-full self-center items-center gap-y-4 my-[2%] p-3 bg-white rounded-xl `}
            showsVerticalScrollIndicator={false}
            data={elements?.data}
            renderItem={({item}: {item: Doc}) => (
              <DocumentCard element={item} width={`ios:w-80 android:w-90`} />
            )}
            keyExtractor={(item: any) => item.id}
            ListEmptyComponent={<NoResults />}
          />
        </View>
      </View>
    </ScrollView>
  );
}
