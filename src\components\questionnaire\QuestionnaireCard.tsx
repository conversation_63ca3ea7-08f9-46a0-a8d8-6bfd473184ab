import {appRoutes, baseUrl} from '@root/src/constants';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import React from 'react';
import {Text, View} from 'react-native';
import {Image} from 'expo-image';
import {useTranslation} from 'react-i18next';
import {truncate} from 'lodash';
import {Doc, Questionnaire} from '@root/src/types';
import {useAppSelector} from '@root/src/redux/hooks';
import {A} from '@expo/html-elements';

type Props = {
  element: Doc | Questionnaire;
  type?: string;
  width?: string;
};

export default function ({element, width = `w-72`, type = `document`}: Props) {
  const {t} = useTranslation();
  const {lang} = useAppSelector(state => state.locale);
  return (
    <A href={`${baseUrl}/${lang}/questionnaire/${element.id}`}>
      <View
        style={tw`flex flex-col bg-white border border-gray-200 ${width}  h-70 rounded-3xl`}>
        <View style={tw`border-b  border-gray-100 h-3/5 rounded-t-xl`}>
          <Image
            style={tw`h-full w-full rounded-t-3xl`}
            contentFit="cover"
            source={{uri: element.thumb}}
          />
        </View>
        <View style={tw`flex flex-col  h-2/5`}>
          <View
            style={tw`flex h-[60%] flex-row justify-between items-start p-3`}>
            <View>
              <Text
                style={tw`text-black text-md text-center font-semibold  capitalize font-alfont leading-loose`}>
                {truncate(element.name, {length: 80})}
              </Text>
            </View>
            <View style={tw`flex-row  justify-start items-center`}>
              <Text
                style={tw`font-alfont capitalize text-sm mx-1 font-bold text-theme-700 text-lg`}>
                {element.price}
              </Text>
              <Text
                style={tw`font-alfont capitalize text-sm font-bold text-theme-800`}>
                {t('kd')}
              </Text>
            </View>
          </View>
          <View
            style={tw`relative h-[40%] bottom-0 flex border-t-[0.1] border-gray-300 flex-row justify-center items-center bg-gray-50 rounded-b-3xl`}>
            <View
              style={tw` border-r w-1/2 border-gray-300 items-center justify-center h-full`}>
              <Text
                style={tw`font-alfont capitalize text-xs text-center truncate `}>
                {t('purchase')}
              </Text>
            </View>
            <View style={tw`  w-1/2 items-center justify-center h-full`}>
              <Text
                style={tw`font-alfont text-xs text-center truncate capitalize`}>
                {t('show_details')}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </A>
  );
}
