{"name": "striveedu", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "prettier": "prettier --write --no-bracket-spacing --single-quote --jsx-bracket-same-line --print-width 80 src/*/*/*  app/*/*/*"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/config-plugins": "~8.0.0", "@expo/html-elements": "^0.10.1", "@expo/prebuild-config": "~7.0.0", "@expo/vector-icons": "^14.0.0", "@gorhom/bottom-sheet": "^4", "@hookform/resolvers": "^3.6.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/netinfo": "11.3.1", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.0.2", "@reduxjs/toolkit": "^2.2.5", "@sentry/react-native": "^6.5.0", "@types/lodash": "^4.17.4", "axios": "^1.7.2", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.4.5", "eas-cli": "^14.2.0", "expo": "~51.0.31", "expo-asset": "~10.0.10", "expo-av": "~14.0.7", "expo-checkbox": "^3.0.0", "expo-constants": "~16.0.2", "expo-dev-client": "~4.0.25", "expo-device": "~6.0.2", "expo-font": "~12.0.8", "expo-image": "~1.12.15", "expo-linear-gradient": "~13.0.2", "expo-linking": "~6.3.1", "expo-localization": "^15.0.3", "expo-notifications": "~0.28.16", "expo-router": "~3.5.23", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-storage": "^47.0.0", "expo-system-ui": "~3.0.7", "expo-updates": "~0.25.24", "expo-web-browser": "~13.0.3", "i18next": "^22.4.14", "i18next-browser-languagedetector": "^8.0.0", "lodash": "^4.17.21", "lottie-react-native": "6.7.0", "moment": "^2.30.1", "native-notify": "^4.0.4", "patch-package": "^8.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.52.1", "react-i18next": "^12.2.0", "react-native": "0.74.5", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.16.1", "react-native-iap": "^12.14.2-rc.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-paper": "^5.12.3", "react-native-permissions": "^4.1.5", "react-native-reanimated": "~3.10.1", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-svg": "15.2.0", "react-native-svg-transformer": "^1.4.0", "react-native-swiper": "^1.6.0", "react-native-toast-message": "^2.2.0", "react-native-web": "~0.19.10", "react-native-webview": "13.8.6", "react-redux": "^9.1.2", "redux": "^5.0.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-saga": "^1.3.0", "rn-fetch-blob": "^0.12.0", "twrnc": "^4.2.0", "validate.js": "^0.13.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "@types/redux-logger": "^3.0.13", "jest": "^29.2.1", "jest-expo": "~51.0.4", "react-test-renderer": "18.2.0", "typescript": "~5.3.3"}, "private": true}