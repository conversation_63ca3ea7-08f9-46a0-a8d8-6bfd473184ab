import tw from '@root/tailwind';
import {images} from '@src/constants';
import {Image} from 'expo-image';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  return (
    <View
      style={tw`  flex-col w-full justify-center items-center   bg-white rounded-xl p-3`}>
      <Image source={images.no_results} style={tw`w-60  h-60`} />
      <Text
        style={tw`text-base w-40 text-center font-alfont capitalize  text-black font-bold`}>
        {t('no_results')}
      </Text>
    </View>
  );
}
