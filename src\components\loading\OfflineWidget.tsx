import IonIcon from '@expo/vector-icons/Ionicons';
import tw from '@root/tailwind';
import * as Updates from 'expo-updates';
import React from 'react';
import {ActivityIndicator, Pressable, Text, View} from 'react-native';

const OfflineWidget = () => {
  return (
    <View
      style={{
        height: '100%',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: `white`,
      }}>
      <ActivityIndicator />
      <Pressable
        onPress={() => Updates.reloadAsync()}
        style={tw`flex justify-center items-center my-4 border border-gray-300 rounded-lg p-3`}>
        <IonIcon name={`wifi`} color="black" size={20} />
        <Text style={tw`font-alfont text-black mt-4`}>{'reconnect'}</Text>
      </Pressable>
    </View>
  );
};

export default OfflineWidget;
