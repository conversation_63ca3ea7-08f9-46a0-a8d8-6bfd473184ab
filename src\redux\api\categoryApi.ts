import {apiSlice} from '@src/redux/api/apiSlice';
import {AppQueryResult, CategoriesType, Tag} from '@root/src/types';
import {keepCache} from '@src/constants';
import {isUndefined} from 'lodash';

export const categoryApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getCategories: builder.query<
      AppQueryResult<CategoriesType[]>,
      {[key: string]: string | number} | void
    >({
      query: params => ({
        url: `category`,
        ...(!isUndefined(params) ? {params: {...params}} : {}),
      }),
    }),
    getTags: builder.query<
      AppQueryResult<Tag[]>,
      {[key: string]: string | number} | void
    >({
      query: params => ({
        url: `tag`,
        ...(!isUndefined(params) ? {params: {...params}} : {}),
      }),
    }),
    getCategory: builder.query<AppQueryResult<CategoriesType[]>, {id: string}>({
      query: ({id}) => ({
        url: `category`,
        params: {id},
      }),
      keepUnusedDataFor: keepCache,
    }),
  }),
});

export const {
  useLazyGetCategoriesQuery,
  useGetCategoriesQuery,
  useGetCategoryQuery,
  useGetTagsQuery,
} = categoryApi;
