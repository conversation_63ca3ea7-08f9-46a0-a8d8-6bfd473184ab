import {A} from '@expo/html-elements';
import tw from '@root/tailwind';
import {images, whatsappUrl} from '@src/constants';
import {Image} from 'expo-image';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';
import {useAppSelector} from '@src/redux/hooks';

const JoinUs = () => {
  const {t} = useTranslation();
  const {setting} = useAppSelector(state => state.appSetting);

  return (
    <View style={tw`bg-theme-200  rounded-xl mb-3  p-3`}>
      <View style={tw`flex-row justify-between py-3`}>
        <Image source={images.join_us} style={tw`w-40  h-28`} />
        <View style={tw`flex-col  items-center `}>
          <Text
            style={tw`text-base w-40 text-center font-alfont capitalize  text-black font-bold`}>
            {t('joinus_message')}
          </Text>
          <View style={tw`my-3 p-2 px-4 bg-theme-800 rounded-xl shadow-md`}>
            <A href={whatsappUrl(setting?.hr_mobile ?? setting?.mobile)}>
              <Text
                style={tw`text-sm  text-white p-2  font-alfont capitalize text-center font-bold`}>
                {t('join_us')}
              </Text>
            </A>
          </View>
        </View>
      </View>
    </View>
  );
};

export default JoinUs;
