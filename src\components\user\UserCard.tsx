import {appRoutes} from '@root/src/constants';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import React from 'react';
import {Image, Text, View} from 'react-native';

type Props = {
  element: any;
  width?: number;
};

export default function ({element, width = undefined}: Props) {
  return (
    <Link href={appRoutes(`${element.id}?name=${element.name}`).userShow}>
      <View
        style={tw` flex-col justify-center items-center  rounded-xl my-[5%]`}>
        <Image
          style={tw`h-14 w-14 rounded-full `}
          source={{uri: element.thumb}}
        />
        <Text
          style={tw`text-black font-alfont capitalize text-xs py-3 text-center font-bold  truncate`}>
          {element.name}
        </Text>
      </View>
    </Link>
  );
}
