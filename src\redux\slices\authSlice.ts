import {Auth} from '@app_types/index';
import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {RootState} from '@src/redux/store';

const initialState: Auth = {
  id: 0,
  email: ``,
  api_token: null,
  name: ``,
  mobile: ``,
  image: ``,
  grade_id: 0,
  player_id: ``,
  roles: {
    id: 0,
    name: ``,
  },
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    login: (state: typeof initialState, action: PayloadAction<Auth>) => {
      if (action.payload?.id) {
        return {
          ...action.payload,
        };
      } else {
        return initialState;
      }
    },

    logout: (
      state: typeof initialState,
      action: PayloadAction<void | undefined>,
    ) => {
      return initialState;
    },
  },
});

export const {login, logout} = authSlice.actions;

export const isAuthenticated = (state: RootState) =>
  state.auth.id !== 0 || state.auth.roles.id !== 0;
export const getAuth = (state: RootState) => state.auth;
