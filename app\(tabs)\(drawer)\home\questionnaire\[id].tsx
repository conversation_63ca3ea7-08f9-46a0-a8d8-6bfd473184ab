import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import {appRoutes} from '@root/src/constants';
import {useGetQuestionnaireQuery} from '@root/src/redux/api/questionnaireApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {
  resetCart,
  setCartElements,
  setCartKind,
  setStudent,
} from '@root/src/redux/slices/cartSlice';
import {
  showErrorToastMessage,
  showSuccessToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {Course, Questionnaire} from '@root/src/types';
import tw from '@root/tailwind';
import {
  Link,
  useLocalSearchParams,
  useNavigation,
  useRouter,
} from 'expo-router';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {Text, TouchableOpacity, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {
    auth,
    cart: {total},
  } = useAppSelector(state => state);
  const {id}: Partial<{id: string}> = useLocalSearchParams();

  const params = useLocalSearchParams();
  const {navigate} = useRouter();

  const {
    data: element,
    isFetching,
    isSuccess,
  } = useGetQuestionnaireQuery<{
    data: Questionnaire;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(id);

  useEffect(() => {
    if (total > 0 && isSuccess) {
      dispatch(resetCart());
    }
  }, []);

  const handleAddToCart = () => {
    // if (auth && element.price > 0) {
    dispatch(setCartKind('questionnaire'));
    dispatch(
      setStudent({
        email: auth.email,
        mobile: auth.mobile,
        name: auth.name,
      }),
    );
    dispatch(setCartElements([element]));
    dispatch(
      showSuccessToastMessage({
        content: t('course_added_to_ur_cart_sucessfully'),
      }),
    );
    navigate(appRoutes().cart);
    // } else {
    //   dispatch(showErrorToastMessage({content: t('u_r_not_authenticated')}));
    // }
  };

  if (!isSuccess || isFetching || !element) return <MainLoadingView />;

  return (
    <View style={tw`flex flex-1`}>
      <Text>Questionnaire Show {element.id}</Text>
      <Text>Questionnaire Name {element.name}</Text>
      <Link href={appRoutes().questionnaireIndex}>
        Go To Questionnaire Index
      </Link>
      <TouchableOpacity
        style={tw`btn-default`}
        onPress={() => handleAddToCart()}
        // disabled={auth.id === 0}
      >
        <Text style={tw`font-alfont text-white text-md`}>
          {t('add_to_cart')}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
