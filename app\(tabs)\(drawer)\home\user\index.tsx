import CategorySection from '@root/src/components/category/CategorySection';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import Pagination from '@root/src/components/Pagination';
import TransText from '@root/src/components/TransText';
import UserCard from '@root/src/components/user/UserCard';
import {useGetUsersQuery} from '@root/src/redux/api/userApi';
import {AppQueryResult, User} from '@root/src/types';
import tw from '@root/tailwind';
import {useLocalSearchParams} from 'expo-router';
import {isEmpty} from 'lodash';
import {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, RefreshControl, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const query: [{[key: string]: string | number}] | any = useLocalSearchParams<{
    query?: string | [];
  }>();
  const [page, setPage] = useState<string | number>(query?.page ?? 1);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetUsersQuery<{
    data: AppQueryResult<User[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>({
    page,
    ...query,
  });

  if (isFetching) return <MainLoadingView />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      style={tw`mx-3 rounded-xl `}
      contentContainerStyle={tw`flex w-full flex-grow android:pb-26 justify-start  self-center items-center gap-y-4 my-[2%] p-3 bg-white rounded-xl`}
      showsVerticalScrollIndicator={false}
      contentInset={tw.style(`ios:bottom-26`)}
      ListHeaderComponentStyle={tw`flex w-full`}
      ListFooterComponentStyle={tw`flex relative bottom-0 w-full flex-row-reverse justify-center items-center my-2 mx-10 p-3`}
      columnWrapperStyle={tw`flex w-full justify-between items-center `}
      data={elements?.data}
      numColumns={2}
      renderItem={({item}: {item: User}) => <UserCard element={item} />}
      keyExtractor={(item: any) => item.id}
      ListEmptyComponent={<NoResults />}
      ListHeaderComponent={
        <View style={tw`flex flex-col gap-y-4`}>
          {!isEmpty(elements?.data) && (
            <CategorySection query={{group: 'user'}} type={'user'} />
          )}
          <View style={tw`w-full`}>
            <TransText
              title={t('users')}
              className={`text-lg font-alfont capitalize text-prim-800 text-left`}
            />
          </View>
        </View>
      }
      ListFooterComponent={() =>
        !isEmpty(elements) && elements.meta ? (
          <Pagination
            currentPage={elements.meta.current_page}
            lastPage={elements.meta.last_page}
            page={page}
            setPage={setPage}
          />
        ) : (
          <></>
        )
      }
    />
  );
}
