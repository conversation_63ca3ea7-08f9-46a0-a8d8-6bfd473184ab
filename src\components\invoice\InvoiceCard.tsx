import {FontAwesome6} from '@expo/vector-icons';
import {appRoutes} from '@root/src/constants';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, TouchableOpacity, View} from 'react-native';

type Props = {
  element: any;
  width?: string;
};

export default function ({element, width = `w-72`}: Props) {
  const {isRTL} = useAppSelector(state => state.locale);
  const {t} = useTranslation();
  return (
    <Link
      href={
        appRoutes(`${element.id}?name=${t('invoice_no')} ${element.id}`)
          .profileInvoice
      }>
      <View
        style={tw`flex-row border-b border-gray-200 w-full justify-between items-center ${width}`}>
        <View style={tw`  flex-row justify-start gap-x-2 items-center`}>
          <TouchableOpacity
            style={tw`h-10 w-10 flex justify-center items-center p-1.5 rounded-full border border-gray-300`}>
            <FontAwesome6
              size={18}
              color={tw.color(`gray-500`)}
              style={tw``}
              name={isRTL ? 'clipboard-check' : 'clipboard-check'}
            />
          </TouchableOpacity>
          <View style={tw`flex-col  mt-1 gap-1 `}>
            <View style={tw`flex-row gap-x-2 justify-start items-center`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                {t('invoice_no')} :
              </Text>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                #{element.id} -
              </Text>
            </View>
            <View>
              <View style={tw`flex-row gap-x-2 justify-start items-center`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                  {t('date')} :
                </Text>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {element.paid_at} -
                </Text>
              </View>
              <View style={tw`flex-col gap-y-0.5`}>
                <View style={tw`flex-row justify-start items-center gap-x-0.5`}>
                  <Text
                    style={tw`font-alfont text-sm text-left  capitalize text-black font-bold  `}>
                    {t('reference_id')} :
                  </Text>
                  <Text
                    style={tw`font-alfont text-sm text-left  capitalize text-gray-500`}>
                    {element.reference_id} -
                  </Text>
                </View>
                <View style={tw`flex-row justify-start items-center gap-x-0.5`}>
                  <Text
                    style={tw`font-alfont text-sm text-left  capitalize text-black font-bold  `}>
                    {t('kind')} :
                  </Text>
                  <Text
                    style={tw`font-alfont text-sm text-left  capitalize text-gray-500`}>
                    {element.kind} -
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </View>
        <View style={tw`flex-col justify-center gap-y-2`}>
          <TouchableOpacity
            style={tw`border rounded-xl border-gray-200 p-0.5 justify-center items-center`}>
            <Text
              style={tw`font-alfont text-xs text-theme-800 capitalize font-bold `}>
              {t(element.status)}
            </Text>
          </TouchableOpacity>
          <View style={tw`flex-row justify-start items-center `}>
            <Text
              style={tw`font-alfont px-0.5 text-center text-sm text-black capitalize font-bold `}>
              {element.net_total}
            </Text>
            <Text
              style={tw`font-alfont capitalize text-sm font-bold text-theme-800`}>
              {t('kd')}
            </Text>
          </View>
        </View>
      </View>
    </Link>
  );
}
