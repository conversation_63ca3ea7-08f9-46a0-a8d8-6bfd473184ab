import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { I18nManager } from 'react-native'; // Import I18nManager from React Native
import * as ar from './locales/ar/translation.json';
import * as en from './locales/en/translation.json';

// Determine initial language based on the RTL layout
const initialLanguage = I18nManager.isRTL ? 'ar' : 'en';

i18n
  .use(initReactI18next)
  .init({
    defaultNS: 'translation',
    compatibilityJSON: 'v3',
    fallbackLng: 'en',
    debug: true,
    lng: initialLanguage, // Set initial language based on RTL layout
    resources: {
      ar: ar,
      en: en,
    },
    interpolation: {
      escapeValue: false,
    },
    react: {
      useSuspense: true,
    },
  });

export default i18n;
