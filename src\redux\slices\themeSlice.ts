import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {themeSliceType} from '@root/src/types/index';
import {RootState} from '@root/src/redux/store';

const initialState: themeSliceType = {
  isDark: false,
  mode: 'light',
  primary: `#12816E`,
  secondary: `#FC8700`,
  degree: 100, // light = 100 // dark = 800
  variantDegree: 200,
};

export const themeSlice = createSlice({
  name: 'theme',
  initialState,
  reducers: {
    setTheme: (
      state: typeof initialState,
      action: PayloadAction<
        Omit<themeSliceType, 'isDark' | 'degree' | 'variantDegree'>
      >,
    ) => {
      const {mode, secondary, primary} = action.payload;
      return {
        ...state,
        mode: mode,
        isDark: mode === 'dark',
        secondary,
        primary,
        degree: mode === `dark` ? 900 : 100,
        variantDegree: mode === `dark` ? 600 : 200,
      };
    },
    toggleDark: (
      state: typeof initialState,
      action: PayloadAction<boolean>,
    ) => {
      const isDark = action.payload;
      return {
        ...state,
        isDark,
        mode: isDark ? 'dark' : 'light',
        degree: isDark ? 900 : 100,
        variantDegree: isDark ? 200 : 600,
      };
    },
  },
});

export const {setTheme, toggleDark} = themeSlice.actions;

export const themeColors = (state: RootState) => ({
  primary: state.theme.primary,
  secondary: state.theme.secondary,
});
