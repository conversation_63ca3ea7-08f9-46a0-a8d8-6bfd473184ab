export type AppQueryResult<T> = {
  success: boolean;
  status: string | number;
  message: string;
  msg?: string;
  data: T;
  meta?: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
  links?: {};
};

export interface Category {
  id: number | string;
  name: string;
  thumb: string;
}
export interface Tree {
  id: number | string;
  name: string;
  thumb: string;
  url?: string;
  [key: string]: any;
}

export interface Invoice {
  id: number | string;
  status: string;
  kind: string;
  paid: boolean;
  total: number;
  discount: number;
  net_total: number;
  name: string;
  email: string;
  mobile: string;
  [key: string]: any;
}

export interface AppSetting {
  id: number | string;
  name: string;
  image: string;
  [key: string]: any;
}
export interface Product {
  active: boolean;
  description_ar: string;
  description_en: string;
  exclusive: boolean;
  has_attributes: boolean;
  has_real_attributes: boolean;
  id: number;
  image: string;
  images?: Image[];
  isOnSale: boolean;
  name_ar: string;
  name_en: string;
  on_new: boolean;
  price: number;
  qty: number;
  sale_price: number;
  isOneSale: boolean;
  is_limited: boolean;
  sku: string;
  categories?: Category[];
  user: User;
  variants?: variant[];
  [key: string]: any;
}

export type CategoryChildren = Category[];

export interface Slide {
  id: number;
  name: string;
  description: string;
  active: string;
  order: string;
  image: string;
  path: string;
  url: string;
  thumb: string;
}

export interface CategoriesType {
  id: number;
  name: string;
  image: string;
  order: number;
  on_home: string;
  active: string;
  parent_id: number;
  group: string;
  thumb: string;
  large: string;
}

export interface locale {
  lang: string;
  isRTL: boolean;
  dir: string;
  otherLang: string;
  //userChoosed: boolean, // Added to track if the user has chosen a language
}
export interface Section {
  id: string | number;
  name: string;
  sessions: CourseSession[];
}

export interface CourseSession {
  id: string | number;
  name: string;
  notes: string;
  course_id: number;
  user_id: number;
  contract_id: number;
  section_id: number;
  price: number;
  [key: string]: any;
}

// {Added by Ahsan}
interface Course {
  id: string | number;
  name: string;
  description: string;
  is_ticket: boolean;
  image: string;
  intro: string;
  max_attendee: number;
  start_date: string;
  end_date: string;
  active: boolean;
  subject_id: number;
  user_id: number;
  contract_id: number;
  hall_id: number;
  group: string;
  on_home: boolean;
  on_sale: number;
  sale_price: number;
  teacher: Teacher;
  hall: Hall;
  subject: Subject;
  documents: Doc[];
  total_price: number;
  thumb: string;
  large: string;
  is_expired: boolean;
  [key: string]: any;
}

interface Grade {
  id: string | number;
  name: string;
  active: string;
  on_home: string;
  image: string;
  order: string;
  stage_id: string;
  stage: Stage
}
interface Stage {
  id: string | number;
  name: string;
  active: boolean;
  on_home: string;
  image: string;
  order: string;
  group: string;
  large: string;
  thumb: string;
}

interface Teacher {
  id: string | number;
  username: string;
  name: string;
  caption: string;
  description: string;
  email: string;
  mobile: string;
  dob: string;
  civil_id: string;
  nationality: string;
  account_no: string;
  school_name: string;
  active: boolean;
  gender: string;
  image: string;
  title_id: number;
  grade_id: number;
  parent_id: number;
  on_home: boolean;
  roles: {
    id: number;
    name: string;
  };
  title: {
    id: number;
    name: string;
    abbreviation: string;
  };
  age: number;
  is_teacher: boolean;
  large: string | null;
  thumb: string;
}

interface Hall {
  id: string | number;
  name: string;
  image: string;
}

interface Subject {
  id: string | number;
  name: string;
  description: string;
  grade_id: number;
  image: string;
  order: number;
  active: boolean;
  on_home: boolean;
  pivot: Pivot
  grade?: Grade;
  tags: Tag[]
  large: string | null;
  thumb: string;
}

export interface Grade {
  id: number;
  name: string;
  active: boolean;
  on_home?: boolean;
  image?: string;
  order: number;
  stage_id: number;
  thumb: string;
  stage?: Stage
}

export interface Stage {
  id: number;
  name: string;
  active: boolean;
  on_home: boolean;
  image?: string;
  order?: number;
  group?: string;
  large?: string;
  thumb?: string;
}

export interface Tag {
  id: string | number;
  name: string
  order: number
  image: string
  active: boolean
  group: string
  pivot: Pivot2
}

export interface Pivot2 {
  taggable_type: string
  taggable_id: number
  tag_id: number
}

interface Document {
  id: string | number;
  name: string;
  description: string;
  image: string;
  path: string;
  price: number;
  active: boolean;
  pivot: {
    documentable_type: string;
    documentable_id: number;
    document_id: number;
  };
  subject: any[];
  thumb: string;
  large: string;
}

interface CourseResponse {
  element: Course;
  sections: Section[];
  documents: Doc[];
  orders: any[];
}

interface Section {
  id: string | number;
  name: string;
  notes: string;
  order: number;
  sessions: Session[];
}

interface Session {
  id: string | number;
  name: string;
  notes: string;
  course_id: number;
  user_id: number;
  contract_id: number;
  section_id: number;
  price: number;
  slot_id: number;
  date: string;
  start: string;
  end: string;
  url: string;
  order: number;
  status: string;
}

// { End }


export interface Pivot {
  documentable_type: string;
  documentable_id: number;
  document_id: number;
}

export interface Hall {
  id: string | number;
  name: string;
  [key: string]: any;
}
export interface coursesTypeSliderImages {
  id: number;
  name: string;
  type: string;
  query?: Query;
  showFront: boolean;
}

export interface Doc {
  id: string | number;
  name: string;
  description: string;
  image: string;
  path: string | URL;
  price: number
  active: boolean
  subject?: Subject
  tags: Tag[]
  thumb: string
  large: string
}

export interface Questionnaire {
  id: string | number;
  name: string;
  thumb: string;
  price: number;
}
export interface User {
  id: number;
  username: string;
  name: string;
  caption: string;
  description: string;
  email: string;
  mobile: string;
  dob: string;
  civil_id: string;
  nationality: string;
  account_no: string;
  school_name: string;
  active: boolean;
  gender: string;
  image: string;
  title_id: number;
  grade_id: number;
  parent_id: number;
  on_home: boolean;
  roles: Roles;
  teacher_courses: TeacherCourse[]
  title: Title;
  age: number;
  is_teacher: boolean;
  large: any;
  thumb: string;
}

export interface TeacherCourse {
  id: number
  name: string
  caption: string;
  description: string
  is_ticket: boolean
  image: string
  intro: string
  max_attendee: number
  start_date: string
  end_date: string
  active: boolean
  subject_id: number
  user_id: number
  contract_id: number
  hall_id?: number
  group: string
  on_home: boolean
  on_sale: number
  sale_price: number
  sessions: Session[]
  subject: Subject
  total_price: number
  thumb: string
  large: string
  is_expired: boolean
}

export interface Roles {
  id: number;
  name: string;
}

export interface Title {
  id: number;
  name: string;
}

export interface Query {
  group?: string;
  role?: string;
}

export interface Teacher {
  id: number;
  name: string;
  caption: string;
  description: string;
  email: string;
  image: any;
  thumb: string;
  is_online: boolean;
  active: boolean;
}

export interface Auth {
  id: number | string;
  email: string;
  api_token: string | null;
  name: string;
  description?: string;
  mobile: string;
  grade_id: string | number;
  school_name?: string;
  gender?: string;
  image: string;
  player_id: string | undefined;
  roles: Role;
  isTeacher?: boolean;
  isStudent?: boolean;
  isAdmin?: boolean;
  isSuper?: boolean;
  orders?: Order[] | [];
}

export interface Order {
  id: number;
  reference_id: string;
  status: 'delivered' | 'success' | 'pending';
  net_price: number;
  created_at: Date;
}

export type MainContextType = {
  socket?: any | null | void;
  trans: (name: string) => string;
  getLocalized: (name: string) => string;

  classNames: (classes: [any]) => void;
  currentFont: string | undefined;
  normalText: string;
  btnClass: string;
  mainBgColor: string;
};

export interface Role {
  id: number;
  name: string;
  [key: string]: any;
}

export type toastMessage = {
  content: string;
  type: string | 'default' | 'success' | 'error' | 'warning' | 'info';
  title?: string;
  showToast: boolean;
};

export type Cart = {
  coupon: any;
  net_total: number;
  total: number;
  discount: number;
  cartElements: any;
  element: any;
  user: any;
  kind: 'course' | 'questionnaire' | 'document';
};

export type themeSliceType = {
  isDark: boolean;
  mode: string;
  primary: string;
  secondary: string;
  degree: number;
  variantDegree: number;
};
