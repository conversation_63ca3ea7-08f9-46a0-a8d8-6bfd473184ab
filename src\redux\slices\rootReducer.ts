import {combineReducers} from '@reduxjs/toolkit';
import {apiSlice} from '@root/src/redux/api/apiSlice';
import {authSlice} from '@src/redux/slices//authSlice';
import {bootStrappedSlice} from '@src/redux/slices/bootStrappedSlice';
import {cartSlice} from '@src/redux/slices/cartSlice';
import {localeSlice} from '@src/redux/slices/localeSlice';
import {themeSlice} from '@src/redux/slices/themeSlice';
import {toastMessageSlice} from '@src/redux/slices/toastMessageSlice';
import {appSettingSlice} from '@src/redux/slices/appSettingSlice';

export const rootReducer = combineReducers({
  [localeSlice.name]: localeSlice.reducer,
  [themeSlice.name]: themeSlice.reducer,
  [toastMessageSlice.name]: toastMessageSlice.reducer,
  [bootStrappedSlice.name]: bootStrappedSlice.reducer,
  [cartSlice.name]: cartSlice.reducer,
  [authSlice.name]: authSlice.reducer,
  [appSettingSlice.name]: appSettingSlice.reducer,
  [apiSlice.reducerPath]: apiSlice.reducer,
});
