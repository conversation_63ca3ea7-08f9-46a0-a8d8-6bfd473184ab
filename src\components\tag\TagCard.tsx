import {appRoutes} from '@root/src/constants';
import {Tag} from '@root/src/types';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import {Text, View} from 'react-native';

type Props = {
  element: Tag;
  type: 'document' | 'questionnaire';
};
export default function ({element, type}: Props) {
  return (
    <Link
      href={
        type === 'document'
          ? appRoutes(`tag_id=${element.id}`).documentIndex
          : appRoutes(`tag_id=${element.id}`).questionnaireIndex
      }>
      <View style={tw`bg-theme-100  p-2 px-5 rounded-3xl`}>
        <Text style={tw`font-alfont text-sm capitalize text-theme-700`}>
          {element.name}
        </Text>
      </View>
    </Link>
  );
}
