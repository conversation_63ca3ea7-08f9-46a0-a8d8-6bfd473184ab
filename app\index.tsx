import {Redirect} from 'expo-router';
import * as Updates from 'expo-updates';
import registerNNPushToken from 'native-notify';

export default function () {
  async function onFetchUpdateAsync() {
    try {
      registerNNPushToken(22494, 'ffJbhSyWREafw031ZbWVlR');
      const update = await Updates.checkForUpdateAsync();
      if (update.isAvailable) {
        await Updates.fetchUpdateAsync();
        await Updates.reloadAsync();
      }
    } catch (error) {
      // You can also add an alert() to see the error message in case of an error when fetching updates.
      // console.log(`Error fetching latest Expo update: ${error}`);
    }
  }
  return <Redirect href="(tabs)/(drawer)/home/" />;
}

// reaminings
// 1- coupon form ---->  done
// 2- cart icon (number) 1 ----> done
// 3- trans  ----> done
// 3- equilevant rate test pecentages ---> not like the website
// 3- deep linking
// 4- push notifications test (please refer to native-notify)
// 5- send whatsapp message verification to whatsapp
// 6- test u receiving whatsapp message & email after registration
// 7- test u receiving whatsapp message & email after order is successfull or failure
// 8- general testing throughout the whole app
