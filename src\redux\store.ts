import AsyncStorage from '@react-native-async-storage/async-storage';
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { apiSlice } from '@root/src/redux/api/apiSlice';
import { authApi } from '@src/redux/api/authApi';
import { categoryApi } from '@src/redux/api/categoryApi';
import { courseApi } from '@src/redux/api/courseApi';
import { documentApi } from '@src/redux/api/documentApi';
import { gradeApi } from '@src/redux/api/gradeApi';
import { questionnaireApi } from '@src/redux/api/questionnaireApi';
import { userApi } from '@src/redux/api/userApi';
import rootSaga from '@src/redux/sagas/rootSaga';
import { rootReducer } from '@src/redux/slices/rootReducer';
import { createLogger } from 'redux-logger';
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRATE,
} from 'redux-persist';
import createSagaMiddleWare from 'redux-saga';

const sagaMiddleWare = createSagaMiddleWare();

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  blacklist: ['api'],
  whitelist: ['theme', 'locale', 'auth', 'appSetting', 'cart'],
  debug: false
};


const persistedReducer = persistReducer(persistConfig, rootReducer);
const appLogger = createLogger({
  collapsed: false,
  duration: false,
});

let store: any = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
      immutableCheck: false,
    }).concat(
      apiSlice.middleware,
      categoryApi.middleware,
      courseApi.middleware,
      gradeApi.middleware,
      userApi.middleware,
      authApi.middleware,
      documentApi.middleware,
      questionnaireApi.middleware,
      sagaMiddleWare,
      appLogger,
    ),
});

sagaMiddleWare.run(rootSaga);
setupListeners(store.dispatch); // NOTE this addition
const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}

export type AppDispatch = typeof store.dispatch;
export { persistor, store };
