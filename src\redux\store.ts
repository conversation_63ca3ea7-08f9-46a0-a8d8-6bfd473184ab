import AsyncStorage from '@react-native-async-storage/async-storage';
import {configureStore} from '@reduxjs/toolkit';
import {setupListeners} from '@reduxjs/toolkit/query';
import {apiSlice} from '@root/src/redux/api/apiSlice';
// Import the API slices to ensure they are injected into the base apiSlice
import '@src/redux/api/authApi';
import '@src/redux/api/categoryApi';
import '@src/redux/api/courseApi';
import '@src/redux/api/documentApi';
import '@src/redux/api/gradeApi';
import '@src/redux/api/questionnaireApi';
import '@src/redux/api/userApi';
import rootSaga from '@src/redux/sagas/rootSaga';
import {rootReducer} from '@src/redux/slices/rootReducer';
import {createLogger} from 'redux-logger';
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDR<PERSON><PERSON>,
} from 'redux-persist';
import createSagaMiddleWare from 'redux-saga';

const sagaMiddleWare = createSagaMiddleWare();

const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  blacklist: ['api'],
  whitelist: ['theme', 'locale', 'auth', 'appSetting', 'cart'],
  debug: false,
};

const persistedReducer = persistReducer(persistConfig, rootReducer);
const appLogger = createLogger({
  collapsed: false,
  duration: false,
});

let store: any = configureStore({
  reducer: persistedReducer,
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
      immutableCheck: false,
    }).concat(
      apiSlice.middleware, // This includes all injected endpoints
      sagaMiddleWare,
      appLogger,
    ),
});

sagaMiddleWare.run(rootSaga);
setupListeners(store.dispatch); // NOTE this addition
const persistor = persistStore(store);

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}

export type AppDispatch = typeof store.dispatch;
export {persistor, store};
