import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {locale} from '@app_types/index';

const initialState: locale = {
  isRTL: true,
  dir: 'rtl',
  lang: 'ar',
  otherLang: 'en',
};

export const localeSlice = createSlice({
  name: 'locale',
  initialState,
  reducers: {
    setLocale: (state: typeof initialState, action: PayloadAction<string>) => {
      return {
        ...state,
        dir: action.payload === 'ar' ? 'rtl' : 'ltr',
        isRTL: action.payload === 'ar' ? true : false,
        lang: action.payload,
        label: action.payload === 'ar' ? 'arabic' : 'english',
        otherLang: action.payload === 'ar' ? 'en' : 'ar',
      };
    },
  },
});

export const {setLocale} = localeSlice.actions;
