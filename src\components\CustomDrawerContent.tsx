import {
  AntDesign,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from '@expo/vector-icons';
import {DrawerContentScrollView} from '@react-navigation/drawer';
import {DrawerActions} from '@react-navigation/native';
import {appRoutes, icons, imageSizes} from '@root/src/constants';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {setLocale} from '@root/src/redux/slices/localeSlice';
import tw from '@root/tailwind';
import {useGetSettingQuery} from '@src/redux/api/apiSlice';
import {setAppSetting} from '@src/redux/slices/appSettingSlice';
import {getAuth, isAuthenticated, logout} from '@src/redux/slices/authSlice';
import {resetCart} from '@src/redux/slices/cartSlice';
import {AppSetting} from '@src/types';
import {Image} from 'expo-image';
import * as expoLocaliaztion from 'expo-localization';
import {Link, useNavigation, useRouter} from 'expo-router';
import {includes, toLower} from 'lodash';
import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {I18nManager, Pressable, Text, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

export default function CustomDrawerContent(props: any) {
  const {bottom} = useSafeAreaInsets();
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const router = useRouter();
  const {t, i18n} = useTranslation();
  const isAuth = useAppSelector(isAuthenticated);
  const {isRTL} = useAppSelector(state => state.locale);
  const auth = useAppSelector(getAuth);
  const {
    data: element,
    isFetching,
    isSuccess,
  } = useGetSettingQuery<{
    data: AppSetting;
    isSuccess: boolean;
    isFetching: boolean;
  }>();
  const {supportedLngs} = i18n.services.resourceStore.data;
  const closeDrawer = () => navigation.dispatch(DrawerActions.closeDrawer());

  useEffect(() => {
    const currentLang = includes(expoLocaliaztion.locale, 'ar') ? 'ar' : 'en';
    dispatch(setLocale(currentLang));
    i18n.changeLanguage(currentLang, err => {
      I18nManager.forceRTL(currentLang === currentLang);
      I18nManager.allowRTL(currentLang === currentLang);
      I18nManager.isRTL = currentLang === currentLang;
    });
  }, [expoLocaliaztion.locale]);

  useEffect(() => {
    if (isSuccess) {
      dispatch(setAppSetting(element));
    }
  }, [isSuccess]);

  const handleLogout = () => {
    dispatch(resetCart());
    dispatch(logout());
    closeDrawer();
  };

  return (
    <View style={tw`flex  flex-1`}>
      <DrawerContentScrollView
        {...props}
        scrollEnabled={true}
        showsVerticalScrollIndicator={false}
        style={tw`flex-1 bg-white rounded-3xl`}
        contentContainerStyle={tw`flex-grow android:pb-36 rounded-3xl`}
        contentInset={tw.style(`ios:bottom-36`)}>
        <View style={tw` p-5 flex-row justify-start items-center w-full`}>
          <Link href={appRoutes().profile}>
            <View style={tw`flex flex-row justify-start items-center`}>
              <View style={tw`rounded-full border border-gray-200 p-3`}>
                {isAuth && includes(auth.image, 'http') ? (
                  <Image
                    source={{uri: auth.image}}
                    style={tw`w-8 h-8 rounded-full`}
                    contentFit="cover"
                  />
                ) : (
                  <icons.account
                    color={tw.color(`gray-400`)}
                    style={tw` ${isRTL && `rotate-180`} h-7 w-7`}
                  />
                )}
              </View>
              <View style={tw`flex flex-col justify-center items-start`}>
                <Text
                  style={tw`text-left font-alfont text-md capitalize text-black font-light mx-1 p-2`}>
                  {isAuth ? auth.name : t('u_r_a_guest')}
                </Text>
                <Text
                  style={tw`text-left font-alfont text-sm capitalize text-theme-400 font-light mx-1 p-2`}>
                  {isAuth ? t(toLower(auth.roles.name)) : t('register_now')}
                </Text>
              </View>
            </View>
          </Link>
        </View>

        <Pressable
          onPress={() => router.push(appRoutes().home)}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.home
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('home')}
            </Text>
          </View>
        </Pressable>

        <Pressable
          onPress={() => router.push(appRoutes(``).courseIndex)}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sideBarCourses
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('courses')}
            </Text>
          </View>
        </Pressable>
        <Pressable
          onPress={() => router.push(appRoutes(``).userIndex)}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sidebar_Consultancy
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('users')}
            </Text>
          </View>
        </Pressable>
        <Pressable
          onPress={() => router.push(appRoutes(``).documentIndex)}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sidebar_forms
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('documents')}
            </Text>
          </View>
        </Pressable>

        <Pressable
          onPress={() => router.push(appRoutes(``).questionnaireIndex)}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sidebar_forms
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('quesitonnaires')}
            </Text>
          </View>
        </Pressable>
        <Pressable
          onPress={() => {
            navigation.dispatch(DrawerActions.closeDrawer());
            router.push(appRoutes().services);
          }}
          style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sidebar_volunteers
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('services')}
            </Text>
          </View>
        </Pressable>

        <Pressable
          onPress={() => {
            navigation.dispatch(DrawerActions.closeDrawer());
            router.push(appRoutes().contactus);
          }}
          style={tw`pt-2 px-5 w-full flex justify-start items-start border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <icons.sidebar_contactUs
              color={tw.color(`gray-500`)}
              style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('contactus')}
            </Text>
          </View>
        </Pressable>
        <Pressable
          onPress={() => {
            navigation.dispatch(DrawerActions.closeDrawer());
            router.push(appRoutes().aboutus);
          }}
          style={tw`pt-2 px-5 w-full flex justify-start items-start border-b border-gray-100`}>
          <View
            style={tw`flex w-full flex-row justify-start items-center py-2`}>
            <AntDesign
              name="infocirlceo"
              size={imageSizes.sm}
              color={tw.color(`gray-500`)}
              style={tw`${isRTL && `rotate-180`} h-6 w-6`}
            />
            <Text
              style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
              {t('aboutus')}
            </Text>
          </View>
        </Pressable>

        {isAuth && (
          <>
            <Pressable
              onPress={() => router.push(appRoutes().profileUpdate)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <icons.sidebar_invoices
                  color={tw.color(`gray-500`)}
                  style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('my_information')}
                </Text>
              </View>
            </Pressable>

            <Pressable
              onPress={() => router.push(appRoutes(``).profileCourses)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <MaterialIcons
                  name="laptop-chromebook"
                  color={tw.color(`gray-500`)}
                  size={imageSizes.xs}
                  style={tw` ${isRTL && `rotate-180`} h-4 w-5 `}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('my_courses')}
                </Text>
              </View>
            </Pressable>

            <Pressable
              onPress={() => router.push(appRoutes().profileDocuments)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <Ionicons
                  name="document-attach-outline"
                  color={tw.color(`gray-500`)}
                  size={imageSizes.xs}
                  style={tw` ${isRTL && `rotate-180`} h-5 w-5 `}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('my_documents')}
                </Text>
              </View>
            </Pressable>

            <Pressable
              onPress={() => router.push(appRoutes().profileQuestionnaires)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <MaterialCommunityIcons
                  name="file-document-edit-outline"
                  color={tw.color(`gray-500`)}
                  size={imageSizes.xs}
                  style={tw` ${isRTL && `rotate-180`} h-5 w-5 `}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('my_questionnaires')}
                </Text>
              </View>
            </Pressable>

            <Pressable
              onPress={() => router.push(appRoutes().profileInvoices)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <icons.sidebar_invoices
                  color={tw.color(`gray-500`)}
                  style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('my_invoices')}
                </Text>
              </View>
            </Pressable>

            <Pressable
              onPress={() => router.push(appRoutes().change_password)}
              style={tw`pt-2 px-5 w-full flex justify-start items-center border-b border-gray-100`}>
              <View
                style={tw`flex w-full flex-row justify-start items-center py-2`}>
                <icons.sidebar_changePassword
                  color={tw.color(`gray-500`)}
                  style={tw` ${isRTL && `rotate-180`} h-6 w-6`}
                />
                <Text
                  style={tw`font-alfont text-md capitalize text-black text-left font-light px-2`}>
                  {t('change_password')}
                </Text>
              </View>
            </Pressable>
          </>
        )}

        <View
          style={tw`mt-[10%] border-b border-gray-300 flex w-full justify-center items-center`}>
          <icons.logoAr
            color={tw.color(`gray-500`)}
            style={tw` ${isRTL && `rotate-180`} h-6 w-6 mb-4`}
          />
        </View>
        {isAuth && (
          <View style={tw`m-3 justify-start items-start w-auto`}>
            <Pressable
              onPress={() => handleLogout()}
              style={tw`bg-white border border-theme-700 rounded-3xl p-3 flex w-full justify-center items-center text-center`}>
              <Text style={tw`text-theme-700 font-alfont text-sm text-center`}>
                {t('logout')}
              </Text>
            </Pressable>
          </View>
        )}
      </DrawerContentScrollView>
    </View>
  );
}
