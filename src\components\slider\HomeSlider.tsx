import {Slide} from '@root/src/types';
import tw from '@root/tailwind';
import {Colors} from '@src/constants';
import {Image} from 'expo-image';
import {useRouter} from 'expo-router';
import {isEmpty, map} from 'lodash';
import React, {useState} from 'react';
import {Text, TouchableOpacity, View} from 'react-native';
import Swiper from 'react-native-swiper';

type Props = {
  slides: Slide[];
};

export default function ({slides}: Props) {
  const router = useRouter();

  return (
    <View style={tw`w-full h-42  my-2 `}>
      {!isEmpty(slides) && (
        <Swiper
          autoplay={true}
          dotColor={tw.color(`text-[${Colors.inActiveDotStyleColor}]`)}
          activeDotColor={tw.color(` text-[${Colors.activeDotColor}] `)}
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          style={tw``}
          activeDotStyle={tw`w-10  h-1.5 top-[8%]`}
          pagingEnabled={true}
          dotStyle={tw`w-6.5 h-1.5 top-[8%]`}
          autoplayTimeout={3}
          bounces={true}>
          {map(slides, (s: Slide, i) => (
            <View key={i} style={tw`w-full h-42`}>
              <Image
                source={{uri: s.image}}
                style={tw`w-full h-full rounded-2xl`}
                contentFit="cover"
              />
              {s.name && (
                <TouchableOpacity
                  onPress={() => {
                    s.url && s.name ? router.push(s.url) : null;
                  }}>
                  <Text
                    style={tw`font-alfont text-theme-700 text-sm text-left relative bottom-10 mx-4`}>
                    {s.name}
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          ))}
        </Swiper>
      )}
    </View>
  );
}
