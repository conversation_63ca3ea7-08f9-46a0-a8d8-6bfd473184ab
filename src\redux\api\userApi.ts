import {AppQueryResult, User} from '@root/src/types';
import {apiSlice} from '@src/redux/api/apiSlice';
import {isUndefined} from 'lodash';

export const userApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getUsers: builder.query<
      AppQueryResult<User[]>,
      {[key: string]: string | number} | void
    >({
      query: params => {
        return {
          url: `user`,
          ...(!isUndefined(params) ? {params: {...params}} : {}),
        };
      },
    }),
    getUser: builder.query<AppQueryResult<User>, string | number | undefined>({
      query: id => {
        return {
          url: `user/${id}`,
        };
      },
    }),
    userEdit: builder.mutation<
      AppQueryResult<User>,
      string | number | undefined
    >({
      query: id => {
        return {
          url: `user/${id}`,
        };
      },
    }),
  }),
});

export const {useGetUsersQuery, useGetUserQuery, useUserEditMutation} = userApi;
