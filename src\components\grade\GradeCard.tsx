import {appRoutes} from '@root/src/constants';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import React from 'react';
import {Image, Text, View} from 'react-native';

type Props = {
  element: any;
  width?: number;
};

export default function ({element, width = undefined}: Props) {
  return (
    <View style={tw`flex flex-col  rounded-xl items-center m-2 `}>
      <Link href={appRoutes(`grade_id=${element.id}`).courseIndex} push>
        <View style={tw`self-center`}>
          <View
            style={tw`border  border-gray-200 self-center rounded-xl p-5.5 `}>
            <Image
              style={tw`h-7 w-7`}
              resizeMode="contain"
              source={{uri: element.thumb}}
            />
          </View>

          <Text
            style={tw`text-gray-500 font-alfont  capitalize  text-xs font-bold text-center  my-2`}>
            {element.name} - {element.id}
          </Text>
        </View>
      </Link>
    </View>
  );
}
