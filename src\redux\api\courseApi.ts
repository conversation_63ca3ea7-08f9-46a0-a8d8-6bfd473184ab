import {AppQueryResult, Course} from '@root/src/types';
import {apiSlice} from './apiSlice';
import {isUndefined} from 'lodash';

export const courseApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getCourses: builder.query<
      AppQueryResult<Course[]>,
      {[key: string]: string | number} | void
    >({
      query: params => {
        return {
          url: `course`,
          ...(!isUndefined(params) ? {params: {...params}} : {}),
        };
      },
    }),
    getCourse: builder.query<AppQueryResult<Course[]>, string | undefined>({
      query: id => {
        return {
          url: `course/${id}`,
        };
      },
    }),
  }),
});

export const {useLazyGetCoursesQuery, useGetCoursesQuery, useGetCourseQuery} =
  courseApi;
