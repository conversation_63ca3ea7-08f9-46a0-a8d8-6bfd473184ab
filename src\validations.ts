import * as yup from 'yup';
yup.setLocale({
  mixed: {
    required: 'validation.required',
    oneOf: 'validation.password_does_not_match',
  },
  number: {
    min: ({min}) => ({key: 'validation.min', values: {min}}),
    max: ({max}) => ({key: 'validation.max', values: {max}}),
  },
  string: {
    email: 'validation.email',
    min: ({min}) => ({key: `validation.min`, values: min}),
    max: ({max}) => ({key: 'validation.max', values: max}),
    matches: 'validation.matches',
  },
});
export const loginSchema = yup.object({
  login: yup.string().required('required'),
  // isKeepLogin: yup.string(),
  password: yup
    .string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters long')
    .max(20)
    .required(),
  remember: yup.boolean(),
});

export const couponSchema = yup.object({
  coupon: yup.mixed().required('required'),
});

export const registerSchema = yup.object({
  username: yup.string().required('required'),
  email: yup.string().email().required('required'),
  mobile: yup.string().required(),
  grade_id: yup.mixed().required('required'),
  password: yup
    .string()
    .required('required')
    .min(6, 'Password must be at least 6 characters long'),
  password_confirmation: yup
    .string()
    .oneOf([yup.ref('password'), 'does_not_match']),
  gender: yup.string(),
  school_name: yup.string(),
});

export const userEditSchema = yup.object({
  username: yup.string().required('required'),
  email: yup.string().email().required('required'),
  mobile: yup.string().required('required'),
  grade_id: yup.mixed().required('required'),
  gender: yup.string().nullable(),
  school_name: yup.string(),
});

export const resetPasswordSchema = yup
  .object({
    password: yup.string().required(),
    password_confirmation: yup
      .string()
      .required()
      .oneOf([yup.ref('password'), 'does_not_match']),
  })
  .required();
