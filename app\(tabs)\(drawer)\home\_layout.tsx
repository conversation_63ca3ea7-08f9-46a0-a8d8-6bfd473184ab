import {AntDesign} from '@expo/vector-icons';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {Stack, useNavigation} from 'expo-router';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';
import {TouchableOpacity} from 'react-native-gesture-handler';

export default function () {
  const {isRTL} = useAppSelector(state => state.locale);
  const {goBack} = useNavigation();
  const {t} = useTranslation();
  return (
    <Stack
      initialRouteName="index"
      screenOptions={{
        header: (props: any) => {
          return (
            <View
              style={tw`flex flex-row justify-start items-center w-full  rounded-xl  h-14`}>
              <View style={tw`w-1/5 flex justify-center items-start h-14 px-3`}>
                <TouchableOpacity
                  hitSlop={100}
                  style={tw`p-3 bg-white rounded-xl`}
                  onPress={() => goBack()}>
                  <AntDesign
                    size={16}
                    color={tw.color(`theme-700`)}
                    name={isRTL ? 'right' : 'left'}
                  />
                </TouchableOpacity>
              </View>
              <View style={tw`w-3/5 flex justify-center items-start h-14`}>
                <Text
                  style={tw`capitalize font-alfont  truncate font-alfont text-lg pt-1`}>
                  {props.route?.params?.name ?? props.options.headerTitle}
                </Text>
              </View>
              <View style={tw`w-1/5 flex justify-center items-start h-14 px-3`}>
                {/* {isRTL && (
                  <TouchableOpacity
                    style={tw`p-3 bg-white rounded-xl`}
                    onPress={() => goBack()}>
                    <AntDesign
                      size={16}
                      color={tw.color(`theme-700`)}
                      name={isRTL ? 'left' : 'right'}
                    />
                    <Text>here</Text>
                  </TouchableOpacity>
                )} */}
              </View>
            </View>
          );
        },
        headerStyle: {
          backgroundColor: tw.color(`bg-transaprent`),
        },
        headerLargeStyle: {
          backgroundColor: tw.color(`bg-gray-100`),
        },
        headerTintColor: tw.color('theme-700'),
        headerTitleStyle: {
          fontWeight: 'bold',
          color: tw.color(`theme-700`),
        },

        headerBackTitleVisible: false,
        headerTransparent: false,
      }}>
      <Stack.Screen
        name="test"
        options={{
          headerTitle: `${t('shopping_cart')}`,
        }}
      />
      <Stack.Screen
        name="index"
        options={{
          headerShown: false,
          headerTitle: `${t('home')}`,
        }}
      />
      <Stack.Screen
        name="cart/index"
        options={{
          headerTitle: `${t('shopping_cart')}`,
        }}
      />
      <Stack.Screen
        name="cart/payment"
        options={{
          headerTitle: `${t('payment_process')}`,
        }}
      />
      <Stack.Screen
        name="course/index"
        options={{
          headerTitle: `${t('courses')}`,
        }}
      />
      <Stack.Screen name="course/[id]" />
      <Stack.Screen
        name="user/index"
        options={{
          headerTitle: `${t('users')}`,
        }}
      />
      <Stack.Screen name="user/[id]" />
      <Stack.Screen
        name="document/index"
        options={{
          headerTitle: `${t('documents')}`,
        }}
      />
      <Stack.Screen name="document/[id]" />
      <Stack.Screen
        name="questionnaire/index"
        options={{
          headerTitle: `${t('questionnaires')}`,
        }}
      />
      <Stack.Screen name="questionnaire/[id]" />
      <Stack.Screen
        name="aboutus"
        options={{
          headerTitle: `${t('aboutus')}`,
        }}
      />
      <Stack.Screen
        name="contactus"
        options={{
          headerTitle: `${t('contactus')}`,
        }}
      />
      <Stack.Screen
        name="login"
        options={{
          headerTitle: `${t('login')}`,
        }}
      />
      <Stack.Screen
        name="register"
        options={{
          headerTitle: `${t('register')}`,
        }}
      />
      {/* profile */}
      <Stack.Screen
        name="profile/index"
        options={{
          headerTitle: `${t('my_information')}`,
        }}
      />
      <Stack.Screen
        name="profile/courses"
        options={{
          headerTitle: `${t('my_courses')}`,
        }}
      />
      <Stack.Screen
        name="profile/invoice/index"
        options={{
          headerTitle: `${t('my_invoices')}`,
        }}
      />
      <Stack.Screen name="profile/invoice/[id]" />
      <Stack.Screen
        name="profile/documents"
        options={{
          headerTitle: `${t('my_documents')}`,
        }}
      />
      <Stack.Screen
        name="profile/questionnaires"
        options={{
          headerTitle: `${t('my_questionnaires')}`,
        }}
      />
      <Stack.Screen
        name="profile/forgot_password"
        options={{
          headerTitle: `${t('forget_password')}`,
        }}
      />
      <Stack.Screen
        name="profile/change_password"
        options={{
          headerTitle: `${t('change_password')}`,
        }}
      />
    </Stack>
  );
}
