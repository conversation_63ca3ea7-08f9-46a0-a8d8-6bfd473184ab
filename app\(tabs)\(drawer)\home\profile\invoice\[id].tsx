import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import ProfileHeader from '@root/src/components/profile/profileHeader';
import {useGetInvoiceQuery} from '@root/src/redux/api/authApi';
import {Invoice} from '@root/src/types';
import tw from '@root/tailwind';
import {useLocalSearchParams} from 'expo-router';
import {useTranslation} from 'react-i18next';
import {ScrollView, Text, View} from 'react-native';

export default function () {
  const {id}: Partial<{id: string}> = useLocalSearchParams();
  const {t} = useTranslation();
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetInvoiceQuery<{
    data: {order: Invoice; element: any};
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(id ?? 0);

  if (isFetching) return <MainLoadingView />;
  const {order, element} = elements;

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-grow mx-auto w-full`}
      contentInset={{bottom: 100}}
      style={tw`rounded-xl m-3`}>
      <View style={tw`flex flex-col bg-white  h-200 rounded-3xl p-3`}>
        <ProfileHeader />

        <View style={tw`flex-row justify-start items-center pt-3 p-3 gap-x-1`}>
          <Text
            style={tw`font-alfont text-left text-lg capitalize text-black font-bold `}>
            {t('receipt')}
          </Text>
          <Text
            style={tw`font-alfont text-lg capitalize  text-left text-black font-bold `}>
            # {order.id}
          </Text>
        </View>
        <View style={tw` bg-gray-50 my-3 p-3 rounded-3xl `}>
          <View style={tw`flex-col  justify-between items-center gap-3`}>
            <View style={tw`flex-row justify-between items-center`}>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('element_name')} :
                </Text>
              </View>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {element.name}
                </Text>
              </View>
            </View>
            <View style={tw`flex-row justify-between items-center`}>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('kind')} :
                </Text>
              </View>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {order.kind}
                </Text>
              </View>
            </View>
          </View>

          {order.kind === 'course' && element && (
            <View style={tw`flex-row justify-between py-3 items-center`}>
              <View style={tw`flex-row justify-start mx-1 items-center`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('subject')} :
                </Text>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {element.subject?.name}
                </Text>
              </View>
              <View style={tw`flex-row justify-start items-center`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('grade')} :
                </Text>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {element.subject?.grade?.name}
                </Text>
              </View>
            </View>
          )}
        </View>

        <View style={tw` bg-gray-50 my-3  rounded-3xl border border-green-400`}>
          <Text
            style={tw`font-alfont text-left text-md capitalize text-black font-bold  p-3`}>
            {t('more_details')}
          </Text>
          <View
            style={tw` bg-white m-3 p-3 rounded-3xl border border-pink-600`}>
            <View style={tw`flex-row justify-between items-center p-3`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                {t('total')}
              </Text>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                {order.total}
              </Text>
            </View>

            <View style={tw`flex-row justify-between items-center p-3`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                {t('discount')}
              </Text>
              <Text
                style={tw`font-alfont text-left text-sm  capitalize text-red-500`}>
                {order.discount}
              </Text>
            </View>

            <View style={tw`border border-gray-100 border-b `}></View>
            <View style={tw`flex-row justify-between items-center  p-3`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                {t('net_total')}
              </Text>
              <Text
                style={tw`font-alfont text-left text-sm  capitalize text-red-500`}>
                {order.net_total}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
