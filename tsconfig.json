{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": "./", "paths": {"@root/*": ["./*"], "@app/*": ["app/*"], "@src/*": ["src/*"], "@app_types/*": ["src/types/*"], "@app_api/*": ["src/redux/api/*"], "@app_slices/*": ["src/redux/slices/*"], "@app_redux/*": ["src/redux/*"], "@app_components/*": ["components/*"]}}, "include": ["**/*.ts", "**/*.tsx", "app/(tabs)/(drawer)/home/<USER>/index.tsx", "i18n/config.js"]}