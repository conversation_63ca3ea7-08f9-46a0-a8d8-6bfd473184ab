import {AppQueryResult, Questionnaire} from '@root/src/types';
import {apiSlice} from '@src/redux/api/apiSlice';
import {isUndefined} from 'lodash';

export const questionnaireApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getQuestionnaires: builder.query<
      AppQueryResult<Questionnaire[]>,
      {[key: string]: string | number} | void
    >({
      query: params => {
        return {
          url: `questionnaire`,
          ...(!isUndefined(params) ? {params: {...params}} : {}),
        };
      },
    }),
    getQuestionnaire: builder.query<
      AppQueryResult<Questionnaire>,
      string | number | undefined
    >({
      query: id => {
        return {
          url: `questionnaire/${id}`,
        };
      },
    }),
  }),
});

export const {useGetQuestionnairesQuery, useGetQuestionnaireQuery} =
  questionnaireApi;
