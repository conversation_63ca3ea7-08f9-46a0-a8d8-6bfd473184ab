import {appRoutes, getImage, searchModules} from '@root/src/constants';
import tw from '@root/tailwind';
import {Image} from 'expo-image';
import {Link} from 'expo-router';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, Text, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const filteredData = searchModules.filter((s: any) => s.showFront);

  const renderItem = ({item, index}: any) => (
    <Link href={appRoutes(`group=${item.query.group}`).courseIndex} key={index}>
      <View style={tw`flex justify-evenly items-center`}>
        <View style={tw`border border-gray-200 rounded-xl mt-2 [h-37] w-[40]`}>
          <View style={tw`p-3 flex w-full items-center`}>
            <Image
              source={getImage(`${item.name}.png`)}
              style={tw`self-center  h-30 w-40`}
              contentFit="cover"
            />
          </View>
        </View>
        <Text
          style={tw`text-sm py-3 text-center font-bold text-black capitalize font-alfont`}>
          {t(item.name)}
        </Text>
      </View>
    </Link>
  );

  return (
    <View style={tw`bg-white  rounded-xl shadow-sm overflow-hidden`}>
      <View style={tw`p-3 flex-row justify-between items-center `}>
        <Text style={tw`font-bold font-alfont capitalize text-xl`}>
          {t('courses_type')}
        </Text>
      </View>

      <FlatList
        data={filteredData}
        renderItem={renderItem}
        keyExtractor={(item: any) => item.id}
        horizontal={true}
        style={tw` `}
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={tw`flex-row`}
        ItemSeparatorComponent={() => <View style={tw`mx-3`}></View>}
      />
    </View>
  );
}
