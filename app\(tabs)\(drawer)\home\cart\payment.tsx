import {useAppDispatch} from '@root/src/redux/hooks';
import {resetCart} from '@root/src/redux/slices/cartSlice';
import tw from '@root/tailwind';
import {useLocalSearchParams} from 'expo-router';
import {useEffect} from 'react';
import {WebView} from 'react-native-webview';

export default function () {
  const dispatch = useAppDispatch();
  const params: any = useLocalSearchParams();
  useEffect(() => {
    dispatch(resetCart());
  }, []);
  return <WebView style={tw`flex flex-1`} source={{uri: `${params.url}`}} />;
}
