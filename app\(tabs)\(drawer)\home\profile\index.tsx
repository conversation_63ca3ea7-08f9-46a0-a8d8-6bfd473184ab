import {AntDesign} from '@expo/vector-icons';
import {yupResolver} from '@hookform/resolvers/yup';
import ProfileHeader from '@root/src/components/profile/profileHeader';
import {appRoutes, icons} from '@root/src/constants';
import {
  useDeleteUserMutation,
  useUpdateMutation,
} from '@root/src/redux/api/authApi';
import {useGetGradesQuery} from '@root/src/redux/api/gradeApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {
  getAuth,
  isAuthenticated,
  login,
  logout,
} from '@root/src/redux/slices/authSlice';
import {
  showErrorToastMessage,
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {AppQueryResult, Grade} from '@root/src/types';
import {userEditSchema} from '@root/src/validations';
import tw from '@root/tailwind';
import {useRouter} from 'expo-router';
import {filter, first, isEmpty, map} from 'lodash';
import {Fragment, useEffect, useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Pressable,
  RefreshControl,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {DefaultTheme, Dialog, Modal, Portal} from 'react-native-paper';

export default function () {
  const {t} = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {isRTL} = useAppSelector(state => state.locale);
  const isAuth = useAppSelector(isAuthenticated);
  const auth = useAppSelector(getAuth);
  const [gradeVisible, setGradeVisible] = useState(false);
  const [genderVisible, setGenderVisible] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(true);
  const [showDeleteUserAccount, setShowDeleteUserAccount] = useState(false);
  const [triggerUpdate] = useUpdateMutation();
  const [triggerDeleteUserAccount, {isLoading}] = useDeleteUserMutation();
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetGradesQuery<{
    data: AppQueryResult<Grade[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>({order_by: 'desc'});
  const {
    handleSubmit,
    control,
    formState: {errors, isValid, touchedFields},
    setValue,
    getValues,
  } = useForm({
    resolver: yupResolver(userEditSchema),
    defaultValues: {
      username: auth.username,
      email: auth.email,
      gender: auth.gender,
      mobile: auth.mobile,
      grade_id: auth.grade_id,
      school_name: auth.school_name,
    },
  });
  const {grade_id, gender} = getValues();

  useEffect(() => {
    if (!isAuth) {
      router.replace(appRoutes().home);
    }
  }, [isAuth]);

  const onSubmit = async (body: any) => {
    await triggerUpdate({
      id: auth.id,
      username: body.username,
      email: body.email,
      mobile: body.mobile,
      gender: body.gender,
      grade_id: body.grade_id,
      school_name: body.school_name,
    }).then((r: any) => {
      if (r.data) {
        dispatch(login(r.data));
        dispatch(showSuccessToastMessage({content: t('process_success')}));
      } else {
        dispatch(showWarningToastMessage({content: r.error?.data?.message}));
      }
    });
  };

  const handleDeleteUserAccount = async () => {
    await triggerDeleteUserAccount(auth?.id).then((r: any) => {
      if (r.data) {
        dispatch(logout(undefined));
        dispatch(showSuccessToastMessage({content: t('process_success')}));
      } else {
        dispatch(showWarningToastMessage({content: r.error?.data?.message}));
      }
    });
  };

  useEffect(() => {
    if (!isEmpty(errors)) {
      dispatch(showErrorToastMessage({content: `${t('process_failure')}`}));
    }
  }, [errors]);

  return (
    <KeyboardAwareScrollView
      enableOnAndroid={true}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      scrollEnabled={true}
      extraScrollHeight={140}
      extraHeight={150}
      showsHorizontalScrollIndicator={false}
      horizontal={false}
      style={tw`flex  m-3 rounded-xl `}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex pb-26 bg-white px-3 rounded-xl w-full`}
      refreshControl={
        <RefreshControl refreshing={false} tintColor={tw.color(`theme-700`)} />
      }>
      <View style={tw`flex flex-col bg-white rounded-3xl p-3`}>
        <ProfileHeader />
        <Text
          style={tw`font-alfont text-left text-base capitalize text-black font-bold pt-3`}>
          {t('my_information')}
        </Text>
      </View>

      <View style={tw`bg-white rounded-3xl`}>
        <View style={tw`flex-col`}>
          <Text
            style={tw`font-alfont font-bold text-left px-2 text-md text-gray-500 capitalize`}>
            {t('student_name')}
          </Text>
          <View style={tw`pt-3 px-1`}>
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <View style={tw`relative`}>
                  <TextInput
                    placeholder={`${t('enter_student_name')}`}
                    placeholderTextColor="gray-400"
                    textAlign={isRTL ? 'right' : `left`}
                    onChangeText={value => {
                      onChange(value);
                    }}
                    onBlur={onBlur}
                    value={value}
                    style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-xl border-gray-300 pl-5 ${
                      isRTL ? 'text-right' : `text-left`
                    }`}
                    secureTextEntry={false}
                    multiline={false}
                  />
                </View>
              )}
              name="username"
            />
            {errors.username && (
              <Text
                style={tw` text-red-800 font-alfont capitalize text-xs  py-3`}>
                * {t(`${errors?.username?.message}`)}
              </Text>
            )}
          </View>

          {/* email */}
          <View style={tw`flex-col pt-5`}>
            <Text
              style={tw`font-alfont text-left px-1 font-bold text-md text-gray-500 capitalize`}>
              {t('email')}
            </Text>
            <View style={tw`pt-3 px-1`}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <View style={tw`relative`}>
                    <TextInput
                      placeholder={`${t('email')}`}
                      placeholderTextColor="gray-400"
                      textAlign={isRTL ? 'right' : `left`}
                      onChangeText={value => {
                        onChange(value);
                      }}
                      onBlur={onBlur}
                      value={value}
                      style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-xl border-gray-300 pl-5 ${
                        isRTL ? 'text-right' : `text-left`
                      }`}
                      secureTextEntry={false}
                      multiline={false}
                    />
                  </View>
                )}
                name="email"
              />

              {errors.email && (
                <Text
                  style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                  * {t(`${errors?.email?.message}`)}
                </Text>
              )}
            </View>
          </View>
          {/* mobile */}
          <View style={tw`flex-col pt-5`}>
            <Text
              style={tw`font-alfont text-left px-1 font-bold text-md text-gray-500 capitalize`}>
              {t('mobile_number')}
            </Text>
            <View style={tw`pt-3 px-1`}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <View style={tw`relative`}>
                    <TextInput
                      placeholder={`${t('enter_mobile_number')}`}
                      placeholderTextColor="gray-400"
                      textAlign={isRTL ? 'right' : `left`}
                      onChangeText={value => {
                        onChange(value);
                      }}
                      onBlur={onBlur}
                      value={value}
                      style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-xl border-gray-300 pl-5 ${
                        isRTL ? 'text-right' : `text-left`
                      }`}
                      secureTextEntry={false}
                      multiline={false}
                    />
                  </View>
                )}
                name="mobile"
              />

              {errors.mobile && (
                <Text
                  style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                  * {t(`${errors?.mobile?.message}`)}
                </Text>
              )}
            </View>
          </View>
          {/* grade */}
          <View style={tw`mt-5`}>
            <TouchableOpacity onPress={() => setGradeVisible(true)}>
              <Text
                style={tw`font-alfont font-bold text-left text-md px-1 text-gray-500 capitalize`}>
                {t('grade')}
              </Text>
            </TouchableOpacity>
            <View style={tw`pt-3 px-1 `}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <Fragment>
                    <TouchableOpacity
                      onPress={() => setGradeVisible(true)}
                      style={tw`relative  w-full border border-gray-300 p-4 rounded-xl`}>
                      <Text style={tw`font-alfont text-gray-600 text-center`}>
                        {grade_id != 0 && isSuccess
                          ? `${
                              first(
                                filter(elements.data, g => g.id === grade_id),
                              )?.name
                            }`
                          : t('choose_grade')}
                      </Text>
                    </TouchableOpacity>
                    <Portal>
                      <Modal
                        visible={gradeVisible}
                        onDismiss={() => setGradeVisible(false)}
                        contentContainerStyle={tw`flex flex-col h-1/4 mx-6  rounded-2xl bg-white justify-start items-start border border-gray-300 `}>
                        <View
                          style={tw`w-full flex-row justify-center items-center`}>
                          <Pressable
                            onPress={() => setGradeVisible(false)}
                            style={tw`absolute top-4 right-4`}>
                            <AntDesign
                              name="close"
                              style={tw`text-gray-400 w-8 h-8`}
                            />
                          </Pressable>
                          <View style={tw`p-3`}>
                            <Text style={tw`font-alfont text-lg text-gray-800`}>
                              {t('choose_grade')}
                            </Text>
                          </View>
                        </View>
                        <ScrollView style={tw`w-full`}>
                          {isSuccess &&
                            map(elements.data, (s: Grade, i: number) => (
                              <Pressable
                                onPress={() => {
                                  setValue('grade_id', s.id);
                                  setGradeVisible(false);
                                }}
                                key={s.id}
                                style={tw`flex justify-center items-start w-full ${
                                  i + 1 < elements.data.length
                                    ? `border-b border-gray-200`
                                    : ``
                                } p-3`}>
                                <Text
                                  style={tw`font-alfont text-gray-600 text-left`}>
                                  {`${s.name}  - (${s.stage.name})`}
                                </Text>
                              </Pressable>
                            ))}
                        </ScrollView>
                      </Modal>
                    </Portal>
                  </Fragment>
                )}
                name="grade_id"
              />
              {errors.grade_id && (
                <Text
                  style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                  * {t(`${errors?.grade_id?.message}`)}
                </Text>
              )}
            </View>
          </View>
          {/* gender */}
          <View style={tw`mt-5`}>
            <TouchableOpacity onPress={() => setGenderVisible(true)}>
              <Text
                style={tw`font-alfont font-bold text-left text-md px-1 text-gray-500 capitalize`}>
                {t('choose_gender')}
              </Text>
            </TouchableOpacity>
            <View style={tw`pt-3 px-1 `}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <Fragment>
                    <TouchableOpacity
                      onPress={() => setGenderVisible(true)}
                      style={tw`relative  w-full border border-gray-300 p-4 rounded-xl`}>
                      <Text style={tw`font-alfont text-gray-600 text-center`}>
                        {gender ? `${t(gender)}` : t('choose_gender')}
                      </Text>
                    </TouchableOpacity>
                    {errors.gender && (
                      <Text
                        style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                        * {t(`${errors?.gender?.message}`)}
                      </Text>
                    )}
                    <Portal>
                      <Modal
                        visible={genderVisible}
                        onDismiss={() => setGenderVisible(false)}
                        contentContainerStyle={tw`flex flex-col h-1/4 mx-6  rounded-2xl bg-white justify-start items-start border border-gray-300 `}>
                        <View
                          style={tw`w-full flex-row justify-center items-center`}>
                          <Pressable
                            onPress={() => setGenderVisible(true)}
                            style={tw`absolute top-4 right-4`}>
                            <AntDesign
                              name="close"
                              style={tw`text-gray-400 w-8 h-8`}
                            />
                          </Pressable>
                          <View style={tw`p-3`}>
                            <Text style={tw`font-alfont text-lg text-gray-800`}>
                              {t('choose_gender')}
                            </Text>
                          </View>
                        </View>

                        <ScrollView style={tw`w-full`}>
                          <Pressable
                            onPress={() => {
                              setValue('gender', 'male');
                              setGenderVisible(false);
                            }}
                            style={tw`flex justify-center items-start w-full border-b border-gray-100 p-3`}>
                            <Text
                              style={tw`font-alfont text-gray-600 text-left`}>
                              {t('male')}
                            </Text>
                          </Pressable>
                          <Pressable
                            onPress={() => {
                              setValue('gender', 'female');
                              setGenderVisible(false);
                            }}
                            style={tw`flex justify-center items-start w-full border-b border-gray-100 p-3`}>
                            <Text
                              style={tw`font-alfont text-gray-600 text-left`}>
                              {t('female')}
                            </Text>
                          </Pressable>
                        </ScrollView>
                      </Modal>
                    </Portal>
                  </Fragment>
                )}
                name="grade_id"
              />
              {errors.grade_id && (
                <Text
                  style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                  * {t(`${errors?.grade_id?.message}`)}
                </Text>
              )}
            </View>
          </View>
          {/* school_name */}
          <View style={tw`mt-5`}>
            <Text
              style={tw`font-alfont font-bold text-left px-1 text-md text-gray-500 capitalize`}>
              {t('academic_Entity')}
            </Text>
            <View style={tw`pt-3 px-1 `}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <View style={tw`relative  w-full `}>
                    <TextInput
                      placeholder={`${t('enter_academic_entity')}`}
                      placeholderTextColor="gray-400"
                      onChangeText={value => onChange(value)}
                      textAlign={isRTL ? 'right' : `left`}
                      onBlur={onBlur}
                      value={value}
                      style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-xl border-gray-300 pl-5 ${
                        isRTL ? 'text-right' : `text-left`
                      }`}
                      multiline={false}
                    />
                  </View>
                )}
                name="school_name"
              />
              {errors.school_name && (
                <Text
                  style={tw` text-red-800 text-left px-1 font-alfont capitalize text-xs py-3`}>
                  * {t(`${errors?.school_name?.message}`)}
                </Text>
              )}
            </View>
          </View>
          <TouchableOpacity
            style={tw`btn-default bg-theme-800  mt-[7%]`}
            onPress={handleSubmit(onSubmit)}>
            <Text style={tw`font-alfont capitalize text-white text-md`}>
              {t('update')}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => setShowDeleteUserAccount(true)}
            style={tw`bg-white p-4 mt-5 rounded-3xl border border-gray-200 flex justify-center items-center`}>
            <Text style={tw`font-alfont text-theme-700 text-md`}>
              {t('delete_account')}
            </Text>
          </TouchableOpacity>

          <Portal>
            <Dialog
              theme={{
                ...DefaultTheme,
              }}
              visible={showDeleteUserAccount}
              onDismiss={() => setShowDeleteUserAccount(false)}
              style={tw`flex flex-col mx-6  rounded-2xl bg-white justify-start items-start border border-gray-300 `}>
              <Dialog.Content>
                <View style={tw`flex justify-center items-center py-3`}>
                  <icons.deleteAccount height={50} width={50} />
                </View>
                <Text style={tw`text-center text-lg`}>
                  {t('delete_account_message')}
                </Text>
              </Dialog.Content>
              <Dialog.Actions
                style={tw`flex flex-row justify-center items-center`}>
                <TouchableOpacity
                  onPress={() => setShowDeleteUserAccount(false)}
                  style={tw`py-2 px-3 text-sm font-medium bg-white rounded-lg border border-gray-200 focus:outline-none `}>
                  <Text style={tw`font-alfont text-gray-500 text-md`}>
                    {t('cancel')}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleDeleteUserAccount}
                  style={tw`py-2 px-3 text-sm font-medium text-center bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:outline-none focus:ring-red-300 dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-900`}>
                  <Text style={tw`font-alfont text-white text-md`}>
                    {t('delete')}
                  </Text>
                </TouchableOpacity>
              </Dialog.Actions>
            </Dialog>
          </Portal>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
}
