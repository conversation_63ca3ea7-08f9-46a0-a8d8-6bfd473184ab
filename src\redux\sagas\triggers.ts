import {takeLatest} from 'redux-saga/effects';
import {bootStrappedSlice} from '../slices/bootStrappedSlice';
import {localeSlice} from '../slices/localeSlice';
import {
  // startGetSettingsScenario,
  startChangeLangScenario,
  startDisableIsLoadingScenario,
  startEnableIsLoadingScenario,
  startResetAppScenario,
} from './appSaga';

export function* triggerDisableLoading() {
  yield takeLatest(
    bootStrappedSlice.actions.disableIsLoading,
    startDisableIsLoadingScenario,
  );
}

export function* triggerEnableIsLoading() {
  yield takeLatest(
    bootStrappedSlice.actions.enableIsLoading,
    startEnableIsLoadingScenario,
  );
}

export function* triggerResetApp() {
  yield takeLatest(bootStrappedSlice.actions.resetApp, startResetAppScenario);
}

export function* triggerChangeLang() {
  yield takeLatest(localeSlice.actions.setLocale, startChangeLangScenario);
}
