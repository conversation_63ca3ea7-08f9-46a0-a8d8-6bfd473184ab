import {AntDesign} from '@expo/vector-icons';
import tw from '@root/tailwind';
import {imageSizes} from '@src/constants';
import {useAppDispatch, useAppSelector} from '@src/redux/hooks';
import {hideToastMessage} from '@src/redux/slices/toastMessageSlice';
import {startCase} from 'lodash';
import React, {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {View} from 'react-native';
import Toast, {BaseToast} from 'react-native-toast-message';

const ToastContainer = () => {
  const {showToast, type, title, content} = useAppSelector(
    state => state.toastMessage,
  );
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        text1NumberOfLines={1}
        text2NumberOfLines={3}
        style={tw`bg-green-600 opacity-86 flex flex-row justify-between items-center h-auto py-3 border-l border-red-800`}
        renderLeadingIcon={() => (
          <AntDesign
            name={`exclamationcircleo`}
            size={imageSizes.xs}
            style={tw`text-white ml-2`}
          />
        )}
        text1Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
        text2Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
      />
    ),
    error: (props: any) => (
      <BaseToast
        {...props}
        text1NumberOfLines={1}
        text2NumberOfLines={3}
        style={tw`bg-red-600 opacity-86 flex flex-row justify-between items-center h-auto py-3`}
        renderLeadingIcon={() => (
          <AntDesign
            name={`exclamationcircleo`}
            size={imageSizes.xs}
            style={tw` text-white ml-2`}
          />
        )}
        text1Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
        text2Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
      />
    ),
    info: (props: any) => (
      <BaseToast
        {...props}
        text1NumberOfLines={1}
        text2NumberOfLines={3}
        style={tw`bg-yellow-400  opacity-86 flex flex-row justify-between items-center h-auto py-3`}
        renderLeadingIcon={() => (
          <AntDesign
            name={`exclamationcircleo`}
            size={imageSizes.xs}
            style={tw` text-white ml-2`}
          />
        )}
        text1Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
        text2Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
      />
    ),
    warning: (props: any) => (
      <BaseToast
        {...props}
        text1NumberOfLines={1}
        text2NumberOfLines={3}
        style={tw`bg-yellow-500 flex flex-row justify-between items-center h-auto py-3`}
        renderLeadingIcon={() => (
          <AntDesign
            name={`exclamationcircleo`}
            size={imageSizes.xs}
            style={tw` text-white ml-2`}
          />
        )}
        text1Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
        text2Style={tw`font-alfont text-white w-full leading-8 text-sm text-left`}
      />
    ),
  };

  useEffect(() => {
    if (showToast) {
      Toast.show({
        type,
        position: 'top',
        visibilityTime: 4000,
        autoHide: true,
        props: {
          text1NumberOfLines: 3,
        },
        text1: `${startCase(`${t(title)}`)}`,
        text2: `${startCase(`${content}`)}`,
      });
    }
  }, [showToast]);

  return (
    <View style={tw`absolute z-50 top-10 w-full h-auto`}>
      <Toast config={toastConfig} onHide={() => dispatch(hideToastMessage())} />
    </View>
  );
};

export default ToastContainer;
