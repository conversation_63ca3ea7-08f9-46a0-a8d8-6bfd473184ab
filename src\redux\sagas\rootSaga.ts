import {fork, take, all} from 'redux-saga/effects';
import {REHYDRATE, PURGE} from 'redux-persist/lib/constants';
import {
  triggerEnableIsLoading,
  triggerDisableLoading,
  triggerResetApp,
  triggerChangeLang,
} from './triggers';

export default function* rootSaga() {
  yield all([
    fork(triggerEnableIsLoading),
    fork(triggerDisableLoading),
    fork(triggerResetApp),
    fork(triggerChangeLang),
  ]);
  yield take(REHYDRATE); // Wait for rehydrate to prevent sagas from running with empty store
  yield take(PURGE);
}
