import DocumentCard from '@root/src/components/document/DocumentCard';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import ProfileHeader from '@root/src/components/profile/profileHeader';
import {appRoutes} from '@root/src/constants';
import {useGetMyDocumentsQuery} from '@root/src/redux/api/authApi';
import {useAppSelector} from '@root/src/redux/hooks';
import {isAuthenticated} from '@root/src/redux/slices/authSlice';
import {AppQueryResult, Doc} from '@root/src/types';
import tw from '@root/tailwind';
import {useRouter} from 'expo-router';
import {isUndefined} from 'lodash';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, RefreshControl, Text, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const router = useRouter();
  const isAuth = useAppSelector(isAuthenticated);
  const {
    locale: {isRTL},
    auth,
  } = useAppSelector(state => state);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetMyDocumentsQuery<{
    data: AppQueryResult<Doc[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>();

  useEffect(() => {
    if (!isAuth) {
      router.replace(appRoutes().home);
    }
  }, [isAuth]);

  if (isFetching || !isSuccess || isUndefined(elements))
    return <MainLoadingView />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      style={tw`m-3 rounded-3xl`}
      contentContainerStyle={tw`flex flex-grow justify-start items-center gap-y-4 w-full android:pb-26 ios:pb-10 w-full rounded-3xl bg-white`}
      showsVerticalScrollIndicator={false}
      keyExtractor={(item: any) => item.id}
      data={elements?.data}
      contentInset={tw.style(`ios:bottom-26`)}
      ListHeaderComponentStyle={tw`flex flex-col w-full`}
      renderItem={({item}: {item: Doc}) => (
        <DocumentCard
          element={item}
          width={`ios:w-80 android:w-90`}
          download={item.path}
        />
      )}
      ListHeaderComponent={
        <View style={tw`flex flex-col bg-white rounded-3xl p-3`}>
          <ProfileHeader />
          <Text
            style={tw`font-alfont text-left text-base capitalize text-black font-bold pt-3`}>
            {t('my_documents')}
          </Text>
        </View>
      }
      ListEmptyComponent={<NoResults />}
    />
  );
}
