import CustomDrawerContent from '@root/src/components/CustomDrawerContent';
import Drawer from 'expo-router/drawer';
import React from 'react';
import {useTranslation} from 'react-i18next';

export default function () {
  const {t} = useTranslation();

  return (
    <Drawer
      screenOptions={{
        drawerLabelStyle: {
          margin: 5,
        },
        drawerHideStatusBarOnOpen: true,
        // drawerActiveBackgroundColor: 'gray',
        // drawerActiveTintColor: 'white',
        // drawerInactiveTintColor: 'white'
      }}
      drawerContent={CustomDrawerContent}>
      <Drawer.Screen
        name="home"
        options={{
          headerShown: false,
          drawerLabel: `${t('home')}`,
          title: `${t('home')}`,
        }}
      />
    </Drawer>
  );
}
