import {AntDesign} from '@expo/vector-icons';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import CategoryCard from '@src/components/category/CategoryCard';
import {appRoutes, imageSizes} from '@src/constants';
import {useGetCategoriesQuery} from '@src/redux/api/categoryApi';
import {AppQueryResult, CategoriesType, Category} from '@src/types';
import {Link} from 'expo-router';
import {isUndefined} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, VirtualizedList} from 'react-native';

export default function ({
  query,
  type = 'course',
  showSeeAll = false,
}: any | void) {
  const {t} = useTranslation();
  const {
    locale: {isRTL},
  } = useAppSelector(state => state);
  const {
    data: elements,
    isFetching,
    isSuccess,
  } = useGetCategoriesQuery<{
    data: AppQueryResult<Category[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(query ?? undefined);

  if (
    isFetching ||
    !isSuccess ||
    isUndefined(elements) ||
    !elements.data ||
    elements.data?.length == 0
  )
    return null;

  return (
    <View>
      {elements && elements.data && elements.data.length > 0 ? (
        <View style={tw`bg-white rounded-xl shadow-sm overflow-hidden`}>
          <View style={tw`p-3 flex-row justify-between items-center `}>
            <Text style={tw`font-bold text-xl font-alfont capitalize`}>
              {t('categories')}
            </Text>
            {showSeeAll && (
              <Link
                href={
                  type === 'course'
                    ? appRoutes(``).courseIndex
                    : appRoutes(``).userIndex
                }>
                <View
                  style={tw`flex flex-row justify-center items-center py-1 gap-x-2`}>
                  <Text style={tw`text-sm text-gray-500 font-alfont`}>
                    {t('see_all')}
                  </Text>
                  <AntDesign
                    style={tw` px-1 ${isRTL && `rotate-180`}`}
                    name={isRTL ? 'left' : 'right'}
                    size={imageSizes.xxs}
                    color={'gray'}
                  />
                </View>
              </Link>
            )}
          </View>
          {!isFetching && (
            <VirtualizedList
              data={elements.data}
              getItemCount={() => (elements.data ? elements.data.length : 0)}
              getItem={(data, index) => data[index]}
              renderItem={({item}: {item: CategoriesType}) => (
                <CategoryCard
                  width={55}
                  element={item}
                  key={item?.id}
                  type={type}
                />
              )}
              keyExtractor={item => item.id.toString()}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              horizontal={true}
              initialNumToRender={elements?.data.length}
              contentContainerStyle={tw`overflow-hidden`}
              style={tw`w-full mx-3 overflow-hidden`}
              ItemSeparatorComponent={() => <View style={tw`mx-1`}></View>}
            />
          )}
        </View>
      ) : null}
    </View>
  );
}
