import {appRoutes} from '@root/src/constants';
import {useAppSelector} from '@root/src/redux/hooks';
import {Redirect} from 'expo-router';
import {Fragment} from 'react';

export default function () {
  const auth = useAppSelector(state => state.auth);
  return (
    <Fragment>
      {auth.id === 0 ? (
        <Redirect href={appRoutes().login} />
      ) : (
        <Redirect href={appRoutes().profileUpdate} />
      )}
    </Fragment>
  );
}
