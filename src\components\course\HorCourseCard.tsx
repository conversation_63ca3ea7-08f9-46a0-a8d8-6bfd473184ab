import tw from '@root/tailwind';
import {appRoutes, icons, imageSizes} from '@src/constants';
import {Image} from 'expo-image';
import {Link} from 'expo-router';
import {truncate} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View} from 'react-native';

type Props = {
  element: any;
  type?: 'course' | 'user';
  width?: string;
  order_paid?: number;
};

export default function ({
  element,
  type = 'course',
  width = `w-70`,
  order_paid = 0,
}: Props) {
  const {t} = useTranslation();

  return (
    <Link
      style={tw``}
      href={
        appRoutes(`${element.id}?name=${element.name}&order_paid=${order_paid}`)
          .courseShow
      }>
      <View
        style={tw`flex flex-col bg-white border border-gray-200 ${width}  h-90 rounded-3xl `}>
        <View style={tw`border-b  border-gray-100 h-2/5 rounded-t-xl`}>
          <Image
            style={tw`h-full w-full rounded-t-3xl`}
            contentFit="cover"
            source={{uri: element.thumb}}
          />
        </View>
        <View style={tw`h-3/5 justify-between items-center`}>
          <View style={tw`flex  w-full h-5/6 p-3`}>
            <View style={tw`flex flex-row justify-between items-center`}>
              <Text style={tw`font-alfont text-sm font-bold capitalize`}>
                {element.name}
              </Text>
            </View>
            <View style={tw`h-auto `}>
              <View
                style={tw`h-1/3 flex flex-row justify-between items-center mb-2`}>
                <View style={tw` flex-row  justify-start items-center  `}>
                  <Image
                    style={tw`h-6 w-6 rounded-full`}
                    source={{uri: element.teacher.thumb}}
                  />
                  <Text
                    style={tw`font-alfont capitalize text-sm mx-3 font-bold `}>
                    {element.teacher.name}
                  </Text>
                </View>
                {element.on_sale && element.sale_price > 0 ? (
                  <View style={tw`flex-col items-center`}>
                    <View style={tw`flex-row justify-start items-center`}>
                      <Text
                        style={tw`font-alfont capitalize text-sm mx-1 font-bold text-theme-800`}>
                        {element.sale_price}
                      </Text>
                      <Text
                        style={tw`font-alfont capitalize text-sm font-bold text-theme-800`}>
                        {t('kd')}
                      </Text>
                    </View>

                    <View style={tw`flex-row  justify-start items-center`}>
                      <Text
                        style={tw`font-alfont capitalize text-sm mx-1 font-bold text-red-700 line-through`}>
                        {element.total_price}
                      </Text>
                      <Text
                        style={tw`font-alfont capitalize text-sm font-bold text-theme-800`}>
                        {t('kd')}
                      </Text>
                    </View>
                  </View>
                ) : (
                  <View style={tw`flex-row  justify-start items-center`}>
                    <Text
                      style={tw`font-alfont capitalize text-sm font-bold mx-1 text-theme-800`}>
                      {element.total_price}
                    </Text>
                    <Text
                      style={tw`font-alfont capitalize text-sm font-bold text-theme-800`}>
                      {t('kd')}
                    </Text>
                  </View>
                )}
              </View>
              <View
                style={tw`h-auto flex flex-col justify-evenly items-start gap-y-2`}>
                {/* {Row - 1} */}
                <View style={tw`flex-row gap-y-2  justify-start `}>
                  <View
                    style={tw`w-1/2  gap-x-2 flex-row justify-start items-center`}>
                    <icons.Session
                      style={tw`card-icon `}
                      width={imageSizes.xs}
                      height={imageSizes.xs}
                    />
                    <Text
                      style={tw`font-alfont  font-bold text-xs text-theme-800 truncate capitalize`}>
                      {`${t('sessions')} (${element.sessions_count})`}
                    </Text>
                  </View>

                  <View
                    style={tw`w-1/2 flex-row gap-x-2 justify-start items-center`}>
                    <icons.Attendance
                      style={tw`card-icon `}
                      width={imageSizes.xs}
                      height={imageSizes.xs}
                    />
                    <Text
                      style={tw`font-alfont capitalize font-bold text-xs  text-theme-800 `}>
                      {t(element.group)}
                    </Text>
                  </View>
                </View>
                {/* {Row - 2} */}
                <View style={tw`flex-row gap-y-2  justify-start`}>
                  {element.group === 'attendance' ? (
                    <View
                      style={tw`w-1/2 gap-x-2  flex-row justify-start items-center`}>
                      <icons.Hall
                        style={tw`card-icon text-theme-700`}
                        width={imageSizes.xs}
                        height={imageSizes.xs}
                      />
                      {/* <Text
                        style={tw`font-alfont capitalize font-bold text-xs text-theme-800`}>
                        {element.hall.name}
                      </Text> */}
                    </View>
                  ) : null}
                  {element.group === 'attendance' ? (
                    <View
                      style={tw`w-1/2 flex-row gap-x-2 justify-start items-center`}>
                      <icons.Date
                        style={tw`card-icon `}
                        //   color={}
                        width={imageSizes.xs}
                        height={imageSizes.xs}
                      />
                      <Text
                        style={tw`font-alfont capitalize font-bold text-xs  text-center text-theme-800`}>
                        {element.start_date}
                      </Text>
                    </View>
                  ) : null}
                </View>
                {/* {Row - 3} */}
                <View style={tw`flex-row justify-start `}>
                  <View
                    style={tw`w-1/2 gap-x-2 flex-row justify-start items-center`}>
                    <icons.icon
                      style={tw`card-icon text-theme-700`}
                      width={imageSizes.xs}
                      height={imageSizes.xs}
                    />
                    <Text
                      style={tw`font-alfont capitalize font-bold text-center text-xs text-theme-800  truncate`}>
                      {`${element.is_ticket ? t('consultancy') : t('course')}`}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
          {/* {Line} */}
          <View style={tw`flex h-14 border-t-[0.1] border-gray-300`}>
            <View style={tw`flex-row justify-center items-center `}>
              <View
                style={tw` border-r w-1/3 border-gray-300 items-center justify-center h-full`}>
                <Text
                  style={tw`font-alfont capitalize text-xs text-center truncate `}>
                  {truncate(element.subject.name, {length: 10})}
                </Text>
              </View>
              <View
                style={tw` border-r w-1/3 border-gray-300 items-center justify-center h-full`}>
                <Text
                  style={tw`font-alfont text-xs text-center truncate capitalize`}>
                  {truncate(element.subject.grade.stage.name, {length: 20})}
                </Text>
              </View>
              <View style={tw`  w-1/3 items-center justify-center h-full`}>
                <Text
                  style={tw`font-alfont text-xs text-center truncate capitalize`}>
                  {truncate(element.subject.grade.name, {length: 20})}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </Link>
  );
}
