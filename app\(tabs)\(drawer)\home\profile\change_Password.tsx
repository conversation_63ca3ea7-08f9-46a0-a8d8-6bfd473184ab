import {AntDesign} from '@expo/vector-icons';
import {yupResolver} from '@hookform/resolvers/yup';
import {appRoutes, icons, imageSizes} from '@root/src/constants';
import {usePostAuthMutation} from '@root/src/redux/api/authApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {login} from '@root/src/redux/slices/authSlice';
import {
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {loginSchema} from '@root/src/validations';
import tw from '@root/tailwind';
import {Link, router, useNavigation} from 'expo-router';
import {useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Pressable,
  RefreshControl,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

export default function () {
  const {t} = useTranslation();
  const [triggerPostAuth] = usePostAuthMutation();
  const {otherLang, isRTL} = useAppSelector(state => state.locale);
  const dispatch = useAppDispatch();
  const [isChecked, setChecked] = useState(false);
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(true);
  const [ShowIcon, setShowIcon] = useState(false);
  const [loginError, setloginError] = useState('');
  const navigation = useNavigation();

  const {
    handleSubmit,
    control,
    formState: {errors, isValid, touchedFields},
  } = useForm({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      phoneNum: ``,
      password: ``,
    },
  });

  const postLogin = async () => {
    await triggerPostAuth({
      email: '<EMAIL>',
      password: 'password',
    }).then((r: any) => {
      if (r.data) {
        dispatch(login(r.data));
        dispatch(showSuccessToastMessage({content: t('process_success')}));
      } else {
        dispatch(showWarningToastMessage({content: r.error?.data?.message}));
      }
    });
  };

  return (
    <KeyboardAwareScrollView
      enableOnAndroid={true}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      extraScrollHeight={140}
      extraHeight={150}
      showsHorizontalScrollIndicator={false}
      horizontal={false}
      style={tw`flex-1 m-3 rounded-xl`}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex-grow android:pb-26 bg-white px-3 rounded-xl w-full`}
      contentInset={tw.style(`ios:bottom-26`)}
      refreshControl={
        <RefreshControl refreshing={false} tintColor={tw.color(`theme-700`)} />
      }>
      <icons.login_logo
        color={tw.color(`gray-500`)}
        style={tw` ${
          isRTL && `rotate-180`
        } mt-[5%] self-center justify-center h-6 w-6`}
      />
      <View style={tw`flex-col pt-[10%] gap-y-2`}>
        <View style={tw` `}>
          <icons.New_Password
            color={tw.color(`gray-500`)}
            style={tw` ${
              isRTL && `rotate-180`
            } border border-gray-300  rounded-md  self-center p-3 h-10 w-10 `}
          />
        </View>

        <Text
          style={tw`font-alfont text-left px-1 font-bold pt-5 text-lg text-center text-black capitalize`}>
          {t('reset_password')}
        </Text>

        <Text
          style={tw`font-alfont text-left px-1 font-bold pt-1 text-md text-center text-gray-500 capitalize`}>
          {t('reset_password_text')} !
        </Text>
      </View>

      <View style={tw`flex-col pt-[10%]`}>
        <Text
          style={tw`font-alfont text-left  px-1 font-bold text-md text-gray-500 capitalize`}>
          {t('password ')}
        </Text>
        <View style={tw`pt-3 px-1`}>
          <Controller
            control={control}
            render={({field: {onChange, onBlur, value}}) => (
              <View style={tw`relative`}>
                <TextInput
                  placeholder={`${t('enter_password ')}`}
                  placeholderTextColor="gray-400"
                  textAlign={isRTL ? 'right' : `left`}
                  onChangeText={value => {
                    onChange(value);
                  }}
                  onBlur={onBlur}
                  value={value}
                  style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-md border-gray-300 pl-5 ${
                    isRTL ? 'text-right' : `text-left`
                  }`}
                  secureTextEntry={false}
                  multiline={false}
                />
              </View>
            )}
            name="phoneNum"
          />

          {errors.phoneNum && (
            <Text
              style={tw` text-red-800 text-xs font-alfont capitalize px-1 py-3`}>
              * {t(`${errors?.phoneNum?.message}`)}
            </Text>
          )}
        </View>
      </View>

      <View style={tw`flex-col pt-[10%]`}>
        <Text
          style={tw`font-alfont text-left px-1 font-bold text-md text-gray-500 capitalize`}>
          {t('confirm_password ')}
        </Text>
        <View style={tw`pt-3 px-1`}>
          <Controller
            control={control}
            render={({field: {onChange, onBlur, value}}) => (
              <View style={tw`relative`}>
                <TextInput
                  placeholder={`${t('enter_confirm_password')}`}
                  placeholderTextColor="gray-400"
                  textAlign={isRTL ? 'right' : `left`}
                  onChangeText={value => {
                    onChange(value);
                  }}
                  onBlur={onBlur}
                  value={value}
                  style={tw`w-full border p-4 text-black h-13  font-alfont font-bold capitalize  rounded-md border-gray-300 pl-5 ${
                    isRTL ? 'text-right' : `text-left`
                  }`}
                  secureTextEntry={false}
                  multiline={false}
                />
              </View>
            )}
            name="phoneNum"
          />

          {errors.phoneNum && (
            <Text
              style={tw` text-red-800 font-alfont capitalize px-1 text-xs  py-3`}>
              * {t(`${errors?.phoneNum?.message}`)}
            </Text>
          )}
        </View>
      </View>

      <TouchableOpacity
        style={tw`btn-default mt-[7%]`}
        onPress={() => postLogin()}>
        <Text style={tw`font-alfont capitalize text-white text-md`}>
          {t('set_password')}
        </Text>
      </TouchableOpacity>

      {/* <View style={tw`flex flex-row items-center justify-center w-full py-6`}>
        <Link
          href={appRoutes().login}
          style={tw` justify-center self-center h-20 items-center`}>
          <Pressable style={tw`flex flex-row items-center justify-center`}>
            <AntDesign
              size={imageSizes.xs}
              color={tw.color(`gray-500`)}
              name={isRTL ? 'arrowright' : 'arrowleft'}
            />
            <Text
              style={tw`ml-2 text-md text-center font-alfont capitalize text-gray-500 font-bold`}>
              {t(`back_to_login`)}
            </Text>
          </Pressable>
        </Link>
      </View> */}
      <Pressable
        onPress={() => router.push(appRoutes().login)}
        style={tw`flex flex-row items-center justify-center h-20 py-6`}>
        <AntDesign
          size={imageSizes.xs}
          color={tw.color(`gray-500`)}
          name={isRTL ? 'arrowright' : 'arrowleft'}
        />
        <Text
          style={tw`ml-2 text-md text-center font-alfont capitalize text-gray-500 font-bold`}>
          {t(`back_to_login`)}
        </Text>
      </Pressable>
    </KeyboardAwareScrollView>
  );
}
