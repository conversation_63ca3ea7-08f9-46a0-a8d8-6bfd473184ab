import React, {FC, ReactNode, useEffect} from 'react';
import {SafeAreaView} from 'react-native';
import tw from '@root/tailwind';

import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {DrawerActions, useNavigation} from '@react-navigation/native';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';

interface ContentLayoutPropsType {
  children: ReactNode | undefined;
}
const ContentLayout: FC<ContentLayoutPropsType> = ({children}): JSX.Element => {
  // const {
  //   toastMessage: {showToast},
  //   bootStrapped: {isLoading},
  // } = useAppSelector(state => state);

  const showToast = useAppSelector(state => state.showToast);
  const isLoading = useAppSelector(state => state.isLoading);
  const dispatch = useAppDispatch();

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     if (isLoading) {
  //       dispatch(disableIsLoading());
  //     }
  //   }, 2000);
  //   return () => clearInterval(timer);
  // }, [isLoading]);

  if (isLoading) {
    return <MainLoadingView />;
  }

  return (
    <SafeAreaView style={tw`min-h-screen`}>
      {showToast}
      {children}
    </SafeAreaView>
  );
};

export default ContentLayout;
