import {A} from '@expo/html-elements';
import {
  <PERSON><PERSON><PERSON>,
  Feather,
  FontAwesome,
  FontAwesome5,
  FontAwesome6,
  SimpleLineIcons,
} from '@expo/vector-icons';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import {appRoutes, icons, imageSizes, whatsappUrl} from '@root/src/constants';
import {
  useLazyGetMyCoursesQuery,
  useLazyGetMyDocumentsQuery,
} from '@root/src/redux/api/authApi';
import {useGetCourseQuery} from '@root/src/redux/api/courseApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {isAuthenticated} from '@root/src/redux/slices/authSlice';
import {
  resetCart,
  setCartElements,
  setCartKind,
  setElement,
  setStudent,
} from '@root/src/redux/slices/cartSlice';
import {
  showErrorToastMessage,
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {Document, Section, Session} from '@root/src/types';
import tw from '@root/tailwind';
import {Video} from 'expo-av';
import {
  Link,
  useLocalSearchParams,
  useNavigation,
  useRouter,
} from 'expo-router';
import {
  filter,
  first,
  flatMap,
  flatten,
  includes,
  isUndefined,
  map,
  size,
} from 'lodash';
import {Fragment, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, Text, View} from 'react-native';
import {TouchableOpacity} from 'react-native-gesture-handler';
import {List} from 'react-native-paper';

export default function () {
  const {t} = useTranslation();
  const {id, order_paid}: Partial<{id: string; order_paid: number}> =
    useLocalSearchParams();
  const video: any = useRef(null);
  const [status, setStatus] = useState<any>({});
  const router = useRouter();
  const {
    locale: {isRTL},
    auth,
    cart: {total},
    appSetting: {setting},
  } = useAppSelector(state => state);
  const dispatch = useAppDispatch();
  const [isActiveSession, setIsActiveSession] = useState(false);
  const [isActiveDocument, setIsActiveDocument] = useState(false);
  const isAuth = useAppSelector(isAuthenticated);
  const {setOptions} = useNavigation();
  const [triggerGetMyCourses] = useLazyGetMyCoursesQuery();

  const {data, isFetching, isSuccess} = useGetCourseQuery<{
    data: {element: any; orders: any; sections: any};
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(id);

  useEffect(() => {
    if (total > 0 && isSuccess) {
      dispatch(resetCart());
    }
  }, []);

  if (
    !isSuccess ||
    isFetching ||
    isUndefined(data.element) ||
    isUndefined(data.sections) ||
    isUndefined(data.orders)
  )
    return <MainLoadingView />;
  const {element, sections, orders} = data;

  const handleAddToCart = async () => {
    if (!isAuth) {
      return dispatch(
        showErrorToastMessage({content: t('u_r_not_authenticated')}),
      );
    }
    dispatch(setCartKind('course'));
    dispatch(
      setStudent({
        email: auth.email,
        mobile: auth.mobile,
        name: auth.name,
      }),
    );
    const isPurchased = await triggerGetMyCourses().then((r: any) => {
      if (r && r?.data) {
        const ids = map(r.data.data, 'id');
        return includes(ids, element.id);
      } else {
        return false;
      }
    });
    if (isPurchased) {
      dispatch(
        showWarningToastMessage({
          content: 'element_is_already_purchased_before',
        }),
      );
      router.push(appRoutes(``).profileCourses);
    } else {
      dispatch(setElement(element));
      if (element.on_sale && element.sale_price > 0) {
        const firstSession = first(flatMap(sections, 'sessions'));
        const otherSessions = map(
          flatMap(sections, 'sessions').slice(1, -1),
          s => ({
            id: s.id,
            name: s.name,
            price: 0,
          }),
        );
        dispatch(
          setCartElements([
            {
              ...firstSession,
              price: element.sale_price,
            },
            ...otherSessions,
          ]),
        );
      } else {
        dispatch(setCartElements(flatMap(sections, 'sessions')));
      }
      dispatch(
        showSuccessToastMessage({
          content: t('element_added_to_ur_cart_sucessfully'),
        }),
      );
      router.push(appRoutes().cart);
    }
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-grow mx-auto w-full`}
      contentInset={{bottom: 100}}
      style={tw`rounded-xl m-3`}>
      {includes(element.intro, 'http') && (
        <View style={tw`bg-white  rounded-3xl items-center p-3 w-full my-2`}>
          <View style={tw`items-center justify-center relative`}>
            <Video
              ref={video}
              source={{
                uri: element.intro,
              }}
              useNativeControls
              style={tw`w-[85] rounded-3xl h-[45]`}
              isLooping
              onPlaybackStatusUpdate={status => setStatus(() => status)}
            />
            <View
              style={tw`absolute inset-0  flex justify-center items-center`}>
              <TouchableOpacity
                onPress={() =>
                  status.isPlaying
                    ? video.current.pauseAsync()
                    : video.current.playAsync()
                }>
                <FontAwesome
                  size={imageSizes.md}
                  style={tw`h-10 w-10`}
                  color={tw.color(`white`)}
                  name={status.isPlaying ? 'pause' : 'play'}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}

      <View style={tw`bg-white my-2  rounded-3xl p-3`}>
        <View style={tw`flex-row justify-between items-center`}>
          <View
            style={tw`flex-row justify-start items-center gap-x-2 p-3 w-[80%]`}>
            {/* {Icon & Desc} */}
            <icons.CourseShowBookIcon
              color={tw.color(``)}
              style={tw`h-10 w-10`}
            />
            <Text
              style={tw`font-alfont capitalize text-black font-bold text-left`}>
              {element.name}
            </Text>
          </View>
          {/* {price} */}
          {element.on_sale && element.sale_price > 0 ? (
            <View style={tw`flex px-1 flex-col`}>
              <Text
                style={tw`text-xl text-theme-800 font-alfont capitalize`}>{`${
                element.sale_price
              } ${t('kd')}`}</Text>
              <Text
                style={tw`text-red-700 line-through text-md font-alfont capitalize`}>{`${
                element.total_price
              } ${t('kd')}`}</Text>
            </View>
          ) : (
            <Text
              style={tw`text- px-1 text-theme-800  font-alfont capitalize`}>{`${
              element.total_price
            } ${t('kd')}`}</Text>
          )}
        </View>
        {element.description && (
          <View style={tw`gap-x-2 p-3`}>
            <Text style={tw`body-text font-alfont leading-loose`}>
              {element.description}
            </Text>
          </View>
        )}

        <View style={tw`bg-gray-50 rounded-3xl  flex-grow  p-4 my-2`}>
          <View style={tw`flex-row justify-start items-center gap-x-2  `}>
            <TouchableOpacity
              style={tw`h-8 w-8 flex justify-center items-center p-1 rounded-md border border-gray-300`}>
              <FontAwesome6
                size={18}
                color={tw.color(`gray-500`)}
                style={tw``}
                name={isRTL ? 'people-group' : 'people-group'}
              />
            </TouchableOpacity>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {t(element.group)}
            </Text>
          </View>
          <View style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
            <icons.sessionIcon color={tw.color(``)} style={tw` h-6 w-6`} />
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {element.name}
            </Text>
          </View>
          <View style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
            <TouchableOpacity
              style={tw`h-8 w-8 flex justify-center items-center p-1 rounded-md border border-gray-300`}>
              <FontAwesome5
                size={18}
                color={tw.color(`gray-500`)}
                style={tw``}
                name={isRTL ? 'clock' : 'clock'}
              />
            </TouchableOpacity>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {`${t('sessions')} (${size(flatMap(sections, 'sessions'))})`}
            </Text>
          </View>

          {element.group === 'attendance' && (
            <Fragment>
              <View
                style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
                <TouchableOpacity
                  style={tw`h-8 w-8 flex justify-center items-center p-1 rounded-md border border-gray-300`}>
                  <FontAwesome
                    size={18}
                    color={tw.color(`gray-500`)}
                    style={tw``}
                    name={isRTL ? 'bank' : 'bank'}
                  />
                </TouchableOpacity>
                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {element?.hall?.name}
                </Text>
              </View>
              <View
                style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
                <TouchableOpacity
                  style={tw`h-8 w-8 flex justify-center items-center p-1 rounded-md border border-gray-300`}>
                  <FontAwesome
                    size={18}
                    color={tw.color(`gray-500`)}
                    style={tw``}
                    name={isRTL ? 'calendar' : 'calendar'}
                  />
                </TouchableOpacity>
                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {element.start_date}
                </Text>
              </View>
            </Fragment>
          )}
          <View style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
            <TouchableOpacity
              style={tw`h-8 w-8 flex justify-center items-center p-1 rounded-md border border-gray-300`}>
              <FontAwesome5
                size={18}
                color={tw.color(`gray-500`)}
                style={tw``}
                name={isRTL ? 'book' : 'book'}
              />
            </TouchableOpacity>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {element.subject.name}
            </Text>
          </View>
          <View style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
            <TouchableOpacity
              style={tw`h-8 w-8 flex justify-center items-center p-1.2 rounded-md border border-gray-300`}>
              <Feather
                size={18}
                color={tw.color(`gray-500`)}
                style={tw``}
                name={isRTL ? 'clipboard' : 'clipboard'}
              />
            </TouchableOpacity>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {element.subject.grade.name}
            </Text>
          </View>
          <View style={tw`flex-row pt-3 justify-start items-center gap-x-2  `}>
            <TouchableOpacity
              style={tw`h-8 w-8 flex justify-center items-center p-1.2 rounded-md border border-gray-300`}>
              <SimpleLineIcons
                size={18}
                color={tw.color(`gray-500`)}
                style={tw``}
                name={isRTL ? 'graduation' : 'graduation'}
              />
            </TouchableOpacity>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {element.subject.grade.stage.name}
            </Text>
          </View>
        </View>

        {/* {Teacher} */}
        <View style={tw`bg-gray-50 rounded-3xl  flex-grow  p-4 my-2`}>
          <View style={tw`flex-row justify-start items-center gap-x-2 `}>
            <icons.teacher color={tw.color(``)} style={tw`h-6 w-6`} />
            <TouchableOpacity
              onPress={() =>
                router.replace(
                  appRoutes(
                    `${element.teacher.id}?name=${element.teacher.name}`,
                  ).userShow,
                )
              }
              style={tw`flex-col gap-x-2 `}>
              <Text
                style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                {element.teacher.name}
              </Text>
              <Text
                style={tw`font-alfont text-sm capitalize text-gray-400  text-left`}>
                {element.teacher.caption}
              </Text>
            </TouchableOpacity>
          </View>
          <Text
            style={tw`font-alfont text-sm pt-4 capitalize text-gray-400  text-left`}>
            {element.teacher.description}
          </Text>
        </View>
      </View>

      <View style={tw`bg-white my-2  rounded-3xl  p-3`}>
        {/* sessions */}
        {sections.length !== 0 && (
          <View>
            <View style={tw`flex-row justify-start items-center gap-x-4 p-1`}>
              <View style={tw`rounded-xl border border-gray-300`}>
                <icons.sectionIcon style={tw`h-8 w-8 text-gray-400  `} />
              </View>

              <View style={tw`flex-col `}>
                <Text
                  style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                  {t('course_content')}
                </Text>

                <Text
                  style={tw`text-xs font-alfont capitalize text-gray-500`}>{`${t(
                  'sections',
                )} (${size(sections)}) - ${t('sessions')} (${size(
                  flatMap(sections, 'sessions'),
                )})`}</Text>
              </View>
            </View>
            <View style={tw`w-full rounded-3xl border border-gray-100 my-2`}>
              {map(sections, (s: Section, i: number) => (
                <List.Section style={tw` my-0 py-0`} key={i}>
                  <List.Accordion
                    title={s.name}
                    onPress={() => setIsActiveSession(!isActiveSession)}
                    // left={() => <View style={tw`w-0`}></View>}
                    right={() => (
                      <View
                        style={tw`flex flex-row justify-center items-center my-0 py-0 h-auto`}>
                        <View>
                          <Text
                            style={tw`font-alfont text-xs text-gray-500 capitalize`}>{`${t(
                            'sessions_no',
                          )} (${s.sessions.length})`}</Text>
                        </View>
                        <View style={tw`mx-2`}>
                          <Entypo
                            size={imageSizes.xxs}
                            color={tw.color(`text-gray-500`)}
                            name={
                              isActiveSession ? 'chevron-up' : 'chevron-down'
                            }
                          />
                        </View>
                      </View>
                    )}
                    id={s.id}
                    rippleColor={tw.color('white')}
                    titleStyle={tw`font-alfont h-auto  text-sm flex  justify-center capitalize`}
                    style={tw`flex flex-row justify-between items-center self-center  w-full my-0 py-0 h-10 border-b border-gray-100 bg-gray-50 ${
                      i === 0 ? `rounded-t-3xl border-white` : ``
                    } ${
                      i + 1 === sections.length
                        ? `rounded-b-3xl border-white`
                        : ``
                    }`}>
                    {map(s.sessions, (session: Session, i: number) => (
                      <View
                        key={i}
                        style={tw`flex flex-row justify-center items-center self-center w-full py-3  my-0 py-0 h-10 ${
                          i + 1 === sections.length ? `` : `border-b`
                        } border-gray-100 `}>
                        <A
                          href={
                            order_paid == 1 &&
                            includes(session.url, 'http') &&
                            element.group === 'recorded'
                              ? session.url
                              : `#`
                          }
                          style={tw`flex flex-1 w-full`}>
                          <View style={tw`flex flex-row pt-2 w-full px-[8%]`}>
                            <View style={tw`justify-center items-center `}>
                              {element.group === 'recorded' ? (
                                <icons.video
                                  style={tw`h-6 w-6 text-gray-400`}
                                />
                              ) : (
                                <Feather
                                  name="book-open"
                                  color={tw.color(`gray-500`)}
                                  size={imageSizes.xs}
                                  style={tw`h-5 w-5 `}
                                />
                              )}
                            </View>
                            <View
                              style={tw`flex flex-1 justify-center items-start  px-4`}>
                              <Text
                                style={tw`font-alfont text-left capitalize`}>
                                {session.name}
                              </Text>
                            </View>
                            {order_paid == 1 &&
                              element.group === 'recorded' && (
                                <View style={tw`justify-center items-center `}>
                                  <Entypo
                                    size={imageSizes.xxs}
                                    color={tw.color(`text-gray-500`)}
                                    name={`${
                                      isRTL
                                        ? `chevron-small-left`
                                        : `chevron-small-right`
                                    }`}
                                  />
                                </View>
                              )}
                          </View>
                        </A>
                      </View>
                    ))}
                  </List.Accordion>
                </List.Section>
              ))}
            </View>
          </View>
        )}
      </View>

      <View style={tw`bg-white my-2  rounded-3xl  p-3`}>
        {/* Documents */}
        {element.documents.length !== 0 && (
          <View>
            <View style={tw`flex-row justify-between items-center gap-x-4 p-1`}>
              <Text
                style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
                {t('course_documents')}
              </Text>

              <Text style={tw`text-xs font-alfont capitalize text-gray-500`}>
                {`${t('attachments')} (${element.documents.length})`}
              </Text>
            </View>

            <View style={tw`w-full rounded-3xl border border-gray-100 my-2`}>
              {map(element.documents, (s: Document, i: number) => (
                <List.Section style={tw` my-0 py-0 `} key={i}>
                  <List.Accordion
                    title={s.name}
                    onPress={() => setIsActiveDocument(!isActiveDocument)}
                    right={() => (
                      <View
                        style={tw`flex flex-row justify-center items-center my-0 py-0 h-auto`}>
                        <View style={tw`mx-2`}>
                          <Entypo
                            size={imageSizes.xxs}
                            color={tw.color(`text-gray-500`)}
                            name={
                              isActiveDocument ? 'chevron-up' : 'chevron-down'
                            }
                          />
                        </View>
                      </View>
                    )}
                    id={s.id}
                    rippleColor={tw.color('white')}
                    titleStyle={tw`font-alfont h-auto text-sm flex justify-center capitalize`}
                    style={tw`flex flex-row justify-between items-center self-center  w-full my-0 py-0 h-10 border-b border-gray-100 bg-gray-50 ${
                      i === 0 ? `rounded-t-3xl border-white` : ``
                    } ${
                      i + 1 === sections.length
                        ? `rounded-b-3xl border-white`
                        : ``
                    }`}>
                    <View
                      key={s.id}
                      style={tw`flex flex-row justify-center items-center self-center w-full py-3  my-0 py-0 h-10 ${
                        i + 1 === element.documents.length ? `` : `border-b`
                      } border-gray-100 `}>
                      <A
                        href={
                          order_paid == 1 &&
                          s.path &&
                          element.group === 'recorded'
                            ? s.path
                            : `#`
                        }
                        style={tw`flex flex-1 w-full`}>
                        <View style={tw`flex flex-row pt-2 w-full px-[8%]`}>
                          <View style={tw`justify-center items-center `}>
                            <icons.notes style={tw`h-10 w-10 text-gray-400`} />
                          </View>
                          <View
                            style={tw`flex flex-1 justify-center items-start  px-4`}>
                            <Text
                              style={tw`font-alfont text-left text-xs text-gray-500 truncate capitalize`}>
                              {s.description}
                            </Text>
                          </View>
                          {order_paid == 1 &&
                            s.path &&
                            element.group === 'recorded' && (
                              <View style={tw`justify-center items-center `}>
                                <icons.download
                                  style={tw`h-4 w-4 text-gray-400`}
                                />
                              </View>
                            )}
                        </View>
                      </A>
                    </View>
                  </List.Accordion>
                </List.Section>
              ))}
            </View>
          </View>
        )}
        {element.group === 'recorded' && order_paid == 0 && (
          <TouchableOpacity
            style={tw`btn-default mt-3`}
            onPress={() => handleAddToCart()}
            // disabled={!isAuth}
          >
            <Text style={tw`font-alfont text-white`}>{t('add_to_cart')}</Text>
          </TouchableOpacity>
        )}
        {order_paid == 0 && element.group !== 'recorded' && isAuth && (
          <View
            style={tw`btn-default  mt-3 flex flex-row justify-center items-center w-full`}>
            <A
              style={tw`flex-row w-full`}
              href={whatsappUrl(
                setting.whatsapp,
                `${element.name} - course No. ${element.id}`,
              )}>
              <View style={tw`w-[80%]`}>
                <Text style={tw`font-alfont text-white capitalize px-4`}>
                  {t('contact_customer_service_to_enroll')}
                </Text>
              </View>
              <View>
                <FontAwesome5
                  name="whatsapp"
                  color={tw.color(`white`)}
                  size={imageSizes.xs}
                  style={tw` ${isRTL && `rotate-180`} h-5 w-5 mx-4 `}
                />
              </View>
            </A>
          </View>
        )}
        {element.group !== 'recorded' && !isAuth && (
          <View
            style={tw`btn-default  mt-3 flex flex-row justify-center items-center w-full`}>
            <Link href={appRoutes().login} style={tw`w-full`}>
              <Text style={tw`font-alfont text-white text-center`}>
                {t('login')}
              </Text>
            </Link>
          </View>
        )}
      </View>
    </ScrollView>
  );
}
