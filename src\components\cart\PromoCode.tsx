import {yupResolver} from '@hookform/resolvers/yup';
import {appRoutes} from '@root/src/constants';
import {
  useCreateOrderMutation,
  useLazyGetCouponQuery,
} from '@root/src/redux/api/authApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {getAuth, isAuthenticated} from '@root/src/redux/slices/authSlice';
import {setCoupon} from '@root/src/redux/slices/cartSlice';
import {
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {couponSchema} from '@root/src/validations';
import tw from '@root/tailwind';
import {useRouter} from 'expo-router';
import {isEmpty, lowerCase, lowerFirst, random, startCase} from 'lodash';
import moment from 'moment';
import {Controller, useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Button,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';

type Props = {};

export default function PromoCode({}: Props) {
  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const {isRTL} = useAppSelector(state => state.locale);
  const cart = useAppSelector(state => state.cart);

  const [triggerGetCoupon, {isLoading, isError, error}] =
    useLazyGetCouponQuery();
  const {
    handleSubmit,
    control,
    formState: {errors, isValid, touchedFields},
    data,
    watch,
    getValues,
    setValue,
  }: any = useForm({
    resolver: yupResolver(couponSchema),
    defaultValues: {
      coupon: cart.coupon?.code || '',
    },
  });

  const watchCoupon = lowerFirst(watch('coupon'));

  const onSubmit = async (body: any) => {
    if (cart.coupon?.code && cart.coupon?.code === watchCoupon) {
      dispatch(setCoupon(null));
      setValue('coupon', '');
      dispatch(showSuccessToastMessage({content: t('coupon_removed')}));
    } else {
      await triggerGetCoupon(body.coupon).then((r: any) => {
        if (JSON.stringify(r.data) !== '{}') {
          dispatch(setCoupon(r.data));
          dispatch(showSuccessToastMessage({content: t('coupon_applied')}));
        } else {
          dispatch(setCoupon(null));
          dispatch(
            showWarningToastMessage({
              content: t('coupon_not_correct'),
            }),
          );
        }
      });
    }
  };
  return (
    <View style={tw` bg-gray-50 my-3  rounded-3xl`}>
      <Text
        style={tw`font-alfont text-left text-md capitalize text-black font-bold  p-3`}>
        {t('have_a_coupon')}
      </Text>
      <View style={tw` bg-white m-3 p-2 rounded-3xl `}>
        <View style={tw`flex-col pt-5 `}>
          <Text
            style={tw`font-alfont font-bold text-md text-left px-1  text-gray-500 capitalize`}>
            {t('enter_ur_coupon')}
          </Text>
          <View style={tw`pt-3 px-1`}>
            <View
              style={tw`flex flex-row gap-2 justify-between items-center w-full`}>
              <Controller
                control={control}
                render={({field: {onChange, onBlur, value}}) => (
                  <View style={tw`relative w-4/5`}>
                    <TextInput
                      placeholder={`${t('coupon')}`}
                      placeholderTextColor="gray-400"
                      onChangeText={value => {
                        onChange(value);
                      }}
                      onBlur={onBlur}
                      value={value}
                      style={tw` border p-3 text-black h-12  font-alfont font-bold capitalize  rounded-xl border-gray-300 pl-5 ${
                        isRTL ? 'text-right' : `text-left`
                      }`}
                      multiline={false}
                    />
                  </View>
                )}
                name="coupon"
              />

              <Pressable
                disabled={isLoading}
                onPress={handleSubmit(onSubmit)}
                style={tw`${
                  cart.coupon?.code && cart.coupon?.code === watchCoupon
                    ? 'bg-red-900'
                    : 'bg-theme-700'
                } h-12 px-3 justify-center items-center  rounded-2xl`}>
                <Text style={tw`font-alfont text-md text-white`}>
                  {cart.coupon?.code && cart.coupon?.code === watchCoupon
                    ? t(`remove`)
                    : t(`apply`)}
                </Text>
              </Pressable>
            </View>
            <View>
              {errors?.coupon?.message?.key ? (
                <Text
                  style={tw`body-text text-red-800 text-xs py-2 font-alfont`}>
                  {startCase(
                    `${t(`${errors?.coupon?.message?.key}`, {
                      min: errors?.coupon?.message?.values,
                    })}`,
                  )}
                </Text>
              ) : (
                <Text
                  style={tw`body-text text-red-800 text-xs py-2 font-alfont`}>
                  {startCase(`${t(errors?.coupon?.message)}`)}
                </Text>
              )}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
}
