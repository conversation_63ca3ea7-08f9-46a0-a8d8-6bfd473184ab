import InvoiceCard from '@root/src/components/invoice/InvoiceCard';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import ProfileHeader from '@root/src/components/profile/profileHeader';
import {appRoutes} from '@root/src/constants';
import {useGetMyInvoicesQuery} from '@root/src/redux/api/authApi';
import {useAppSelector} from '@root/src/redux/hooks';
import {isAuthenticated} from '@root/src/redux/slices/authSlice';
import {AppQueryResult, Invoice} from '@root/src/types';
import tw from '@root/tailwind';
import {useRouter} from 'expo-router';
import {isUndefined} from 'lodash';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, RefreshControl, Text, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const router = useRouter();
  const isAuth = useAppSelector(isAuthenticated);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetMyInvoicesQuery<{
    data: AppQueryResult<Invoice[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>();

  useEffect(() => {
    if (!isAuth) {
      router.replace(appRoutes().home);
    }
  }, [isAuth]);

  if (isFetching) return <MainLoadingView />;

  if (isSuccess || isUndefined(elements)) return <NoResults />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl refreshing={!isSuccess} onRefresh={refetch} />
      }
      style={tw`m-3 rounded-3xl`}
      contentContainerStyle={tw`flex flex-grow justify-started items-center gap-y-3 w-full android:pb-26 w-full rounded-3xl bg-white`}
      stickyHeaderHiddenOnScroll={true}
      showsVerticalScrollIndicator={false}
      keyExtractor={(item: any) => item.id}
      data={elements?.data}
      contentInset={tw.style(`ios:bottom-26`)}
      renderItem={({item}: {item: Invoice}) => (
        <InvoiceCard element={item} width={`w-full p-3`} />
      )}
      ListHeaderComponentStyle={tw`flex flex-col w-full`}
      ListHeaderComponent={
        <View style={tw`flex flex-col bg-white rounded-3xl p-3`}>
          <ProfileHeader />
          <Text
            style={tw`font-alfont text-left text-base capitalize text-black font-bold pt-3`}>
            {t('my_invoices')}
          </Text>
        </View>
      }
      ListEmptyComponent={<NoResults />}
    />
  );
}
