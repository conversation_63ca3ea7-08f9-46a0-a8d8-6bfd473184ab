import HomeHeader from '@root/src/components/HomeHeader';
import CategorySection from '@root/src/components/category/CategorySection';
import CourseSection from '@root/src/components/course/CourseSection';
import CourseTypeSection from '@root/src/components/course/CourseTypeSection';
import EquivalentRate from '@root/src/components/equivalentRate/EquivalentRate';
import GradeSection from '@root/src/components/grade/GradeSection';
import HomeSlider from '@root/src/components/slider/HomeSlider';
import UserSection from '@root/src/components/user/UserSection';
import {useGetSlidesQuery} from '@root/src/redux/api/apiSlice';
import {AppQueryResult, Slide} from '@root/src/types';
import tw from '@root/tailwind';
import JoinUs from '@src/components/JoinUs';
import React from 'react';
import {RefreshControl, ScrollView, View} from 'react-native';

export default function () {
  const {
    data: slides,
    isSuccess: slidesIsSuccess,
    isFetching,
    refetch,
  } = useGetSlidesQuery<{
    data: AppQueryResult<Slide[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>();

  return (
    <ScrollView
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      showsVerticalScrollIndicator={false}
      style={tw`flex-1 m-3`}
      contentContainerStyle={tw`flex-grow android:pb-26`}
      contentInset={tw.style(`ios:bottom-26`)}>
      <HomeHeader />
      {/* <Pressable onPress={() => router.push(`(tabs)/(drawer)/home/<USER>
        <View style={tw`btn-default`}>
          <Text>Testing Page with Slot</Text>
        </View>
      </Pressable> */}
      <View style={tw`my-1`}>
        {slidesIsSuccess && slides.data.length > 1 && (
          <HomeSlider slides={slides.data} />
        )}
      </View>
      {/* <View style={tw`my-1`}>
        <CategorySection query={{on_home: 1, group: 'subject'}} />
      </View> */}
      <View style={tw`my-1`}>
        <CourseSection query={{on_home: 1, is_ticket: 0}} />
      </View>
      {/* <View style={tw`my-1`}>
        <GradeSection />
      </View> */}
      <View style={tw`my-1`}>
        <EquivalentRate />
      </View>
      {/* <View style={tw`my-1`}>
        <CourseTypeSection />
      </View> */}
      <UserSection query={{on_home: 1}} />
      <View style={tw`my-2`}>
        <JoinUs />
      </View>
    </ScrollView>
  );
}
