import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {useForm} from '@src/constants';
import {floor, isEqual, map, sum} from 'lodash';
import React, {FormEventHandler, useEffect, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, Text, TextInput, View} from 'react-native';
import {SelectList} from 'react-native-dropdown-select-list';

// Define the initial values
const initialValues: any = {
  standardMarks: {
    high_school_percentage: 100,
    subject_1: 0,
    subject_2: 0,
    subject_3: 0,
    subject_4: 0,
  },
  studentMarks: {
    high_school_percentage: 0,
    subject_1: 100,
    subject_2: 100,
    subject_3: 100,
    subject_4: 100,
  },
  resultMarks: {
    high_school_percentage: 0,
    subject_1: 0,
    subject_2: 0,
    subject_3: 0,
    subject_4: 0,
  },
  rate: 0,
};

const EquivalentRate = () => {
  const {t} = useTranslation();
  const {isRTL} = useAppSelector(state => state.locale);
  const dataDropDown: {
    key: string;
    value: string;
    standardMarks?: any;
  }[] = [
    {key: '0', value: '0%'},
    {key: '1', value: '5%'},
    {key: '2', value: '7.5%'},
    {key: '3', value: '10%'},
    {key: '4', value: '15%'},
    {key: '5', value: '20%'},
  ];

  const submit: FormEventHandler = e => {
    e.preventDefault();
  };

  const {dataState, setData, reset} =
    useForm<typeof initialValues>(initialValues);
  const prevStandardMarksRef = useRef(dataState.standardMarks);
  const prevStudentMarksRef = useRef(dataState.studentMarks);
  const prevResultMarksRef = useRef(dataState.resultMarks);
  const prevRateRef = useRef(dataState.rate);

  const ceil = (value: any, precision: any) => {
    const factor = Math.pow(10, precision);
    return Math.ceil(value * factor) / factor;
  };

  const calculateStandardMarks = (marks: any) => ({
    ...marks,
    high_school_percentage:
      100 -
      Object.keys(marks)
        .filter(key => key !== 'high_school_percentage')
        .reduce((sum, key) => sum + ceil(marks[key], 2), 0),
  });

  const calculateStudentMarks = (standardMarks: any, studentMarks: any) => ({
    ...studentMarks,
    ...(standardMarks.subject_1 === 0 && {subject_1: 100}),
    ...(standardMarks.subject_2 === 0 && {subject_2: 100}),
    ...(standardMarks.subject_3 === 0 && {subject_3: 100}),
    ...(standardMarks.subject_4 === 0 && {subject_4: 100}),
  });

  const updateStandardMarks = () => {
    const newStandardMarks = calculateStandardMarks(dataState.standardMarks);
    if (!isEqual(prevStandardMarksRef.current, newStandardMarks)) {
      prevStandardMarksRef.current = newStandardMarks;
      setData('standardMarks', newStandardMarks);
    }
  };

  const updateStudentMarks = () => {
    const newStudentMarks = calculateStudentMarks(
      dataState.standardMarks,
      dataState.studentMarks,
    );
    if (!isEqual(prevStudentMarksRef.current, newStudentMarks)) {
      prevStudentMarksRef.current = newStudentMarks;
      setData('studentMarks', newStudentMarks);
    }
  };

  const updateResultMarks = () => {
    const standardPercentage = dataState.standardMarks.high_school_percentage;
    const studentPercentage = ceil(
      dataState.studentMarks.high_school_percentage,
      2,
    );

    if (studentPercentage >= 50 && studentPercentage <= 120) {
      const newResultMarks = {
        ...dataState.resultMarks,
        high_school_percentage: floor(
          (standardPercentage / 100) * (studentPercentage / 100) * 100,
          3,
        ),
        subject_1: floor(
          (dataState.standardMarks.subject_1 / 100) *
            (dataState.studentMarks.subject_1 / 100) *
            100,
          3,
        ),
        subject_2: floor(
          (dataState.standardMarks.subject_2 / 100) *
            (dataState.studentMarks.subject_2 / 100) *
            100,
          3,
        ),
        subject_3: floor(
          (dataState.standardMarks.subject_3 / 100) *
            (dataState.studentMarks.subject_3 / 100) *
            100,
          3,
        ),
        subject_4: floor(
          (dataState.standardMarks.subject_4 / 100) *
            (dataState.studentMarks.subject_4 / 100) *
            100,
          3,
        ),
      };

      prevResultMarksRef.current = newResultMarks;
      setData('resultMarks', newResultMarks);
    } else {
      if (studentPercentage === 0 && standardPercentage !== 100) {
        // Uncomment if you want to show a warning toast message
        // dispatch(
        //   showWarningToastMessage({
        //     content: t('student_rate_must_be_at_least_50_to_start_calculating'),
        //   })
        // );
      }
    }
  };

  const updateRate = () => {
    // Ensure all values in resultMarks are numbers
    const values = Object.values(dataState.resultMarks).map((value: any) => {
      const num = parseFloat(value);
      return isNaN(num) ? 0 : num;
    });

    // Debugging: Check the processed values

    // Calculate the new rate
    // const newRate = ceil(
    //   values.reduce((sum, mark) => sum + mark, 0),
    //   2,
    // );
    const newRate = floor(sum(map(dataState.resultMarks, s => floor(s, 3))), 2);

    // Update the rate if conditions are met
    if (floor(dataState.resultMarks.high_school_percentage, 3) > 0) {
      setData({rate: newRate});
    }
  };

  useEffect(updateStandardMarks, [
    dataState.standardMarks.subject_1,
    dataState.standardMarks.subject_2,
    dataState.standardMarks.subject_3,
    dataState.standardMarks.subject_4,
  ]);

  useEffect(updateStudentMarks, [dataState.standardMarks]);

  useEffect(() => {
    updateResultMarks();
  }, [dataState.studentMarks, dataState.standardMarks]);

  useEffect(() => {
    updateRate();
  }, [dataState.resultMarks]);

  const handleSelect = (subject: any, val: any) => {
    const numberOnly = parseFloat(val);
    setData('standardMarks', {
      ...dataDropDown?.find(
        listDropdownObject => listDropdownObject.value === val,
      )?.standardMarks,
      [subject]: numberOnly,
    });
  };

  return (
    <View style={tw`bg-white rounded-xl shadow-sm overflow-hidden`}>
      <View style={tw`p-3`}>
        {/* title calc eq rate */}
        <Text style={tw`font-bold font-alfont text-left capitalize text-xl`}>
          {t('calculate_equivalent_rate')}
        </Text>
        {/* degrees */}
        <ScrollView
          // horizontal={true}
          nestedScrollEnabled={true}
          style={tw`border border-gray-200 pb-10  mt-5 rounded-xl h-[75]`}>
          {/* {High school percentage} */}
          <View style={tw`flex-row border-b gap-x-3 border-gray-200`}>
            <View
              style={tw`bg-emerald-50 rounded-tl-xl w-30 ${
                isRTL ? 'p-5 items-center text-center' : 'p-3'
              }`}>
              <Text
                style={tw`text-xs  font-alfont capitalize text-gray-700 whitespace-nowrap`}>
                {t('high_school_rate')}
              </Text>
            </View>

            <View
              style={tw`flex-row justify-evenly flex-1 w-full  items-center`}>
              <Text style={tw`text-black font-alfont capitalize font-bold`}>
                {`${dataState.standardMarks.high_school_percentage} %`}
              </Text>
              <TextInput
                inputMode="decimal"
                keyboardType="decimal-pad"
                placeholder={`${t('enter_ratio')}`}
                maxLength={3}
                style={tw`p-1.2 mx-1 text-center font-alfont capitalize w-23 border  border-slate-300  rounded-xl`}
                onChangeText={newValue => {
                  setData('studentMarks', {
                    ...dataState.studentMarks,
                    high_school_percentage: newValue,
                  });
                }}
              />
              <View style={{borderBottomWidth: 0.4, paddingBottom: 3}}>
                <Text style={tw` font-alfont capitalize mx-3`}>
                  {`${dataState.resultMarks.high_school_percentage} %`}
                </Text>
              </View>
            </View>
          </View>
          {/* {First Subject percentage} */}
          <View style={tw`flex-row  border-b border-gray-200`}>
            <View
              style={tw`bg-emerald-50 rounded-tl-xl  w-30 ${
                isRTL ? 'p-5 items-center text-center' : 'p-3'
              }`}>
              <Text
                style={tw`text-xs  font-alfont capitalize  text-gray-700 whitespace-nowrap`}>
                {t('subject_1')}
              </Text>
            </View>
            <View style={tw`flex-row justify-evenly mx-2 flex-1 items-center`}>
              <View
                style={tw`flex-row  justify-evenly  w-50  items-center  mx-1 items-center`}>
                <ScrollView>
                  <SelectList
                    setSelected={(val: any) => handleSelect('subject_1', val)}
                    data={dataDropDown}
                    dropdownStyles={tw`w-20  border-slate-300 `}
                    save="value"
                    placeholder={`${t('choose')}`}
                    inputStyles={tw`text-xs self-center font-alfont capitalize`}
                    boxStyles={tw`p-1.2 w-20 bg-gray-50 border-slate-300 `}
                  />
                </ScrollView>
                <TextInput
                  inputMode="decimal"
                  maxLength={3}
                  keyboardType="decimal-pad"
                  placeholder={`${t('enter_ratio')}`}
                  style={tw`p-1.2 font-alfont capitalize mx-1 text-center w-23 border  border-slate-300 rounded-xl`}
                  onChangeText={newValue => {
                    setData('studentMarks', {
                      ...dataState.studentMarks,
                      subject_1: newValue,
                    });
                  }}
                />
              </View>
              <View style={{borderBottomWidth: 0.3, paddingBottom: 3}}>
                <Text
                  style={tw` font-alfont capitalize mx-2`}>{`${dataState.resultMarks.subject_1} %`}</Text>
              </View>
            </View>
          </View>
          {/* { Second Subject percentage} */}
          <View style={tw`flex-row border-b border-gray-200`}>
            <View
              style={tw`bg-emerald-50 rounded-tl-xl w-30 ${
                isRTL ? 'p-5 items-center text-center' : 'p-3'
              }`}>
              <Text style={tw`text-xs font-alfont capitalize  text-gray-700`}>
                {t('subject_2')}
              </Text>
            </View>
            <View style={tw`flex-row justify-evenly mx-2 flex-1 items-center`}>
              <View
                style={tw`flex-row  justify-evenly w-50  items-center  mx-1 items-center`}>
                <ScrollView>
                  <SelectList
                    setSelected={(val: any) => handleSelect('subject_2', val)}
                    data={dataDropDown}
                    dropdownStyles={tw`w-20  border-slate-300 `}
                    save="value"
                    inputStyles={tw`text-xs self-center font-alfont capitalize`}
                    placeholder={`${t('choose')}`}
                    boxStyles={tw`p-1.2 w-20 bg-gray-50 border-slate-300  `}
                  />
                </ScrollView>
                <TextInput
                  inputMode="decimal"
                  keyboardType="decimal-pad"
                  maxLength={3}
                  placeholder={`${t('enter_ratio')}`}
                  style={tw`p-1.2 mx-1 font-alfont capitalize text-center w-23 border  border-slate-300  rounded-xl`}
                  onChangeText={newValue => {
                    setData('studentMarks', {
                      ...dataState.studentMarks,
                      subject_2: newValue,
                    });
                  }}
                />
              </View>
              <View style={{borderBottomWidth: 0.4, paddingBottom: 3}}>
                <Text
                  style={tw` font-alfont capitalize mx-2`}>{`${dataState.resultMarks.subject_2} %`}</Text>
              </View>
            </View>
          </View>
          {/* { third Subject} */}
          <View style={tw`flex-row border-b border-gray-200`}>
            <View
              style={tw`bg-emerald-50 rounded-tl-xl w-30 ${
                isRTL ? 'p-5 items-center text-center ' : 'p-3'
              }`}>
              <Text style={tw`text-xs font-alfont capitalize text-gray-700`}>
                {t('subject_3')}
              </Text>
            </View>
            <View style={tw`flex-row justify-evenly mx-2 flex-1 items-center`}>
              <View
                style={tw`flex-row  justify-evenly w-50  items-center  mx-1 items-center`}>
                <ScrollView>
                  <SelectList
                    setSelected={(val: any) => handleSelect('subject_3', val)}
                    data={dataDropDown}
                    dropdownStyles={tw`w-20  border-slate-300 `}
                    save="value"
                    inputStyles={tw`text-xs  font-alfont capitalize self-center `}
                    placeholder={`${t('choose')}`}
                    boxStyles={tw`p-1.2 w-20 bg-gray-50 border-slate-300  `}
                  />
                </ScrollView>
                <TextInput
                  inputMode="decimal"
                  keyboardType="decimal-pad"
                  maxLength={3}
                  placeholder={`${t('enter_ratio')}`}
                  style={tw`p-1.2 mx-1 font-alfont capitalize text-center w-23 border border-slate-300 rounded-xl`}
                  onChangeText={newValue => {
                    setData('studentMarks', {
                      ...dataState.studentMarks,
                      subject_3: newValue,
                    });
                  }}
                />
              </View>
              <View style={{borderBottomWidth: 0.4, paddingBottom: 3}}>
                <Text
                  style={tw` font-alfont capitalize mx-2`}>{`${dataState.resultMarks.subject_3} %`}</Text>
              </View>
            </View>
          </View>
          {/* {fourth subject} */}
          <View style={tw`flex-row gap-x-2 `}>
            <View
              style={tw`bg-emerald-50 rounded-tl-xl w-30 ${
                isRTL ? 'p-5 items-center text-center' : 'p-3'
              }`}>
              <Text style={tw`text-xs font-alfont capitalize text-gray-700`}>
                {t('subject_4')}
              </Text>
            </View>
            <View
              style={tw`flex-row gap-x-2 justify-evenly mx-2 flex-1 items-center`}>
              <View
                style={tw`flex-row  justify-evenly w-50  items-center  mx-1 items-center`}>
                <ScrollView>
                  <SelectList
                    setSelected={(val: any) => handleSelect('subject_4', val)}
                    data={dataDropDown}
                    dropdownStyles={tw`w-20  border-slate-300 `}
                    save="value"
                    inputStyles={tw`text-xs self-center font-alfont capitalize`}
                    placeholder={`${t('choose')}`}
                    boxStyles={tw`p-1.2 w-20 bg-gray-50 border-slate-300  `}
                  />
                </ScrollView>
                <TextInput
                  inputMode="decimal"
                  keyboardType="decimal-pad"
                  maxLength={3}
                  placeholder={`${t('enter_ratio')}`}
                  style={tw`p-1.2 mx-1 text-center w-23 border  font-alfont capitalize border-slate-300 rounded-xl`}
                  onChangeText={newValue => {
                    setData('studentMarks', {
                      ...dataState.studentMarks,
                      subject_4: newValue,
                    });
                  }}
                />
              </View>
              <View style={{borderBottomWidth: 0.4, paddingBottom: 3}}>
                <Text
                  style={tw`font-alfont capitalize mx-2`}>{`${dataState.resultMarks.subject_4} %`}</Text>
              </View>
            </View>
          </View>
        </ScrollView>
        {/* {Equivalent rate} */}
        <View
          style={tw`bg-emerald-50  p-3 mb-3 mt-[6%] w-80 border border-gray-300 self-center rounded-xl shadow-sm flex-row`}>
          <View
            style={tw`flex-1  justify-center items-center border-r border-gray-300`}>
            <Text
              style={tw`text-lg text-emerald-600 capitalize font-extrabold`}>{`${dataState.rate} `}</Text>
          </View>
          <View style={tw`w-40 justify-center items-center`}>
            <Text
              style={tw`text-lg font-alfont capitalize text-black font-extrabold`}>
              {t('equivalent_rate')}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default EquivalentRate;
