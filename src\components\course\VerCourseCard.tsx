import {Entypo} from '@expo/vector-icons';
import {appRoutes, imageSizes} from '@root/src/constants';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {Image} from 'expo-image';
import {useRouter} from 'expo-router';
import {truncate} from 'lodash';
import {Pressable, Text, View} from 'react-native';

type Props = {
  element: any;
  type?: 'course' | 'user';
  width?: string;
  order_paid?: number;
};

export default function ({
  element,
  type = 'course',
  width = `w-70`,
  order_paid = 0,
}: Props) {
  const router = useRouter();
  const {isRTL} = useAppSelector(state => state.locale);
  return (
    <Pressable
      onPress={() =>
        router.push(
          appRoutes(
            `${element.id}?name=${element.name}&order_paid=${order_paid}`,
          ).courseShow,
        )
      }
      style={tw`px-1 py-3 flex-row justify-center items-center self-center w-full h-15 ${width}`}>
      <View
        style={tw`flex-row p-1.5 flex-1 w-[95%] justify-center items-center `}>
        <View style={tw`px-0.5 items-center`}>
          <Image
            source={element.thumb}
            style={tw`h-10 w-10  rounded-xl border border-gray-100 text-gray-400`}
            contentFit="cover"
          />
        </View>
        <View style={tw`flex-1 px-2 gap-y-2`}>
          <Text
            style={tw`font-alfont text-sm text-black font-bold capitalize text-left `}>
            {truncate(element.name, {
              length: 35,
              omission: '...',
            })}
          </Text>
          <Text
            style={tw`font-alfont text-xs text-gray-500 capitalize text-left`}>
            {truncate(element.description, {
              length: 35,
              omission: '...',
            })}
          </Text>
        </View>
        <View style={tw`items-center`}>
          <Entypo
            name={`chevron-thin-${isRTL ? 'left' : 'right'}`}
            size={imageSizes.xxs}
            color={tw.color(`text-gray-500`)}
          />
        </View>
      </View>
    </Pressable>
  );
}
