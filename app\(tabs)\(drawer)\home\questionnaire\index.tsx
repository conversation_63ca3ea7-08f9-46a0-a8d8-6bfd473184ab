import CategorySection from '@root/src/components/category/CategorySection';
import DocumentCard from '@root/src/components/document/DocumentCard';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import QuestionnaireCard from '@root/src/components/questionnaire/QuestionnaireCard';
import TagSection from '@root/src/components/tag/TagSection';
import {useGetDocsQuery} from '@root/src/redux/api/documentApi';
import {
  useGetQuestionnaireQuery,
  useGetQuestionnairesQuery,
} from '@root/src/redux/api/questionnaireApi';
import {AppQueryResult, Doc, Questionnaire} from '@root/src/types';
import tw from '@root/tailwind';
import TransText from '@src/components/TransText';
import {useLocalSearchParams} from 'expo-router';
import {isUndefined} from 'lodash';
import {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, RefreshControl, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const query: [{[key: string]: string | number}] | any = useLocalSearchParams<{
    query?: string | [];
  }>();
  const [page, setPage] = useState<string | number>(query?.page ?? 1);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetQuestionnairesQuery<{
    data: AppQueryResult<Questionnaire[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(query);

  if (isFetching) return <MainLoadingView />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      ListHeaderComponent={
        <View style={tw`flex flex-col my-2`}>
          <TagSection query={{group: 'questionnaire'}} type="questionnaire" />
          <View style={tw`w-full pt-2`}>
            <TransText
              title={t('questionnaires')}
              className={`text-lg font-alfont capitalize text-prim-800 text-left`}
            />
          </View>
        </View>
      }
      ListHeaderComponentStyle={tw`flex flex-1 w-full h-auto`}
      style={tw`mx-3 rounded-xl`}
      contentContainerStyle={tw`flex justify-center w-full self-center items-center gap-y-4 my-[2%] p-3 bg-white rounded-xl `}
      showsVerticalScrollIndicator={false}
      data={elements?.data}
      renderItem={({item}: {item: Questionnaire}) => (
        <QuestionnaireCard
          element={item}
          width={`ios:w-[100%] android:w-90`}
          type={'questionnaire'}
        />
      )}
      keyExtractor={(item: any) => item.id}
      ListEmptyComponent={<NoResults />}
    />
  );
}
