import {FontAwesome} from '@expo/vector-icons';
import {appRoutes, images} from '@root/src/constants';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {Image} from 'expo-image';
import {Link} from 'expo-router';
import {Text, View} from 'react-native';

export default function () {
  const auth = useAppSelector(state => state.auth);
  return (
    <View style={tw`flex flex-col`}>
      <View style={tw``}>
        <Image
          source={images.bg}
          style={tw`w-[100%] border border-gray-300 rounded-3xl h-35`}
          contentFit="cover"
        />
      </View>
      <View style={tw`flex-row justify-between items-center my-4`}>
        <View style={tw`flex-row w-full flex-1 justify-start items-center`}>
          <View style={tw`relative left-0 top-0 shadow-md mx-1`}>
            <Image
              source={auth.image}
              style={tw` w-10 h-10 rounded-xl border border-gray-200`}
              contentFit="cover"
            />
          </View>
          <View style={tw`flex-col left-2 gap-x-2 `}>
            <Text
              style={tw`font-alfont text-left text-sm capitalize text-black font-bold mb-0.5`}>
              {auth.username}
            </Text>
            <Text
              style={tw`font-alfont text-left text-xs capitalize text-gray-500`}>
              {auth.email}
            </Text>
          </View>
        </View>
        <Link href={appRoutes().profileUpdate}>
          <View
            style={tw`h-8 w-8 flex justify-center items-center p-1.2 mx-2 `}>
            <FontAwesome
              size={18}
              color={tw.color(`theme-500`)}
              style={tw``}
              name={'pencil'}
            />
          </View>
        </Link>
      </View>
    </View>
  );
}
