import {FontAwesome} from '@expo/vector-icons';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import ProfileHeader from '@root/src/components/profile/profileHeader';
import QuestionnaireCard from '@root/src/components/questionnaire/QuestionnaireCard';
import {appRoutes, icons, images} from '@root/src/constants';
import {useGetMyQuestionnairesQuery} from '@root/src/redux/api/authApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {isAuthenticated} from '@root/src/redux/slices/authSlice';
import {AppQueryResult, Questionnaire} from '@root/src/types';
import tw from '@root/tailwind';
import {useRouter} from 'expo-router';
import {isUndefined} from 'lodash';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {
  FlatList,
  Image,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const isAuth = useAppSelector(isAuthenticated);
  const {isRTL} = useAppSelector(state => state.locale);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetMyQuestionnairesQuery<{
    data: AppQueryResult<Questionnaire[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>();

  useEffect(() => {
    if (!isAuth) {
      router.replace(appRoutes().home);
    }
  }, [isAuth]);

  if (isFetching) return <MainLoadingView />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      style={tw`m-3 rounded-3xl`}
      contentContainerStyle={tw`flex flex-grow justify-start items-center gap-y-4 w-full android:pb-26 ios:pb-10 w-full rounded-3xl bg-white`}
      showsVerticalScrollIndicator={false}
      keyExtractor={(item: any) => item.id}
      data={elements?.data}
      contentInset={tw.style(`ios:bottom-26`)}
      renderItem={({item}: {item: Questionnaire}) => (
        <QuestionnaireCard
          element={item}
          width={`ios:w-80 android:w-90`}
          type="questionnaire"
        />
      )}
      ListHeaderComponentStyle={tw`flex flex-col w-full`}
      ListHeaderComponent={
        <View style={tw`flex flex-col bg-white rounded-3xl p-3`}>
          <ProfileHeader />
          <Text
            style={tw`font-alfont text-left text-base capitalize text-black font-bold pt-3`}>
            {t('my_questionnaires')}
          </Text>
        </View>
      }
      ListEmptyComponent={<NoResults />}
    />
  );
}
