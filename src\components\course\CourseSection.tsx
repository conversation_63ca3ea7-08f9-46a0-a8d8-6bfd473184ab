import {AntDesign} from '@expo/vector-icons';
import {useGetCoursesQuery} from '@root/src/redux/api/courseApi';
import {useAppSelector} from '@root/src/redux/hooks';
import {AppQueryResult, Course} from '@root/src/types';
import tw from '@root/tailwind';
import HorCourseCard from '@src/components/course/HorCourseCard';
import {appRoutes, imageSizes} from '@src/constants';
import {Link} from 'expo-router';
import {isUndefined} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, VirtualizedList} from 'react-native';

export default function ({query}: any | void) {
  const {
    data: elements,
    isFetching,
    isSuccess,
  } = useGetCoursesQuery<{
    data: AppQueryResult<Course[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(query ?? undefined);
  const {t} = useTranslation();
  const {
    locale: {isRTL},
  } = useAppSelector(state => state);

  if (
    isFetching ||
    !isSuccess ||
    isUndefined(elements) ||
    !elements?.data ||
    elements.data?.length == 0
  )
    return null;

  return (
    <View style={tw`bg-white rounded-xl shadow-sm overflow-hidden`}>
      <View style={tw`p-4  flex-row justify-between items-center`}>
        <Text style={tw`font-bold text-xl font-alfont capitalize`}>
          {t('courses')}
        </Text>
        <View style={tw`flex-row justify-center items-center`}>
          <Link href={appRoutes(``).courseIndex}>
            <Text style={tw`text-sm text-gray-500 capitalize font-alfont`}>
              {t('see_all')}
            </Text>
          </Link>
          <AntDesign
            style={tw`mx-1 ${isRTL && `rotate-180`}`}
            name={isRTL ? 'left' : 'right'}
            size={imageSizes.xxs}
            color={'gray'}
          />
        </View>
      </View>
      {elements && elements.data && !isFetching && (
        <VirtualizedList
          data={elements.data}
          getItemCount={() => (elements.data ? elements.data.length : 0)}
          getItem={(data, index) => data[index]}
          renderItem={({item}: {item: Course}) => (
            <HorCourseCard width={`w-72`} element={item} key={item?.id} />
          )}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          initialNumToRender={elements.data?.length}
          contentContainerStyle={tw``}
          style={tw`w-full p-4 mx-2`}
          ItemSeparatorComponent={() => <View style={tw`mx-2`}></View>}
        />
      )}
    </View>
  );
}
