import {createSlice, PayloadAction} from '@reduxjs/toolkit';
import {AppSetting} from '@src/types';

const initialState: {searchModule: any; setting: AppSetting} = {
  searchModule: {},
  setting: {
    id: 0,
    name: ``,
    image: ``,
  },
};

export const appSettingSlice = createSlice({
  name: 'appSetting',
  initialState,
  reducers: {
    setSearchModule: (
      state: typeof initialState,
      action: PayloadAction<any>,
    ) => {
      return {
        ...state,
        searchModule: action.payload,
      };
    },
    setAppSetting: (
      state: typeof initialState,
      action: PayloadAction<AppSetting>,
    ) => {
      return {
        ...state,
        setting: action.payload,
      };
    },
  },
});

export const {setSearchModule, setAppSetting} = appSettingSlice.actions;
