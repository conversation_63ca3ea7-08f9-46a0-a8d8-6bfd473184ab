import {appRoutes} from '@root/src/constants';
import tw from '@root/tailwind';
import {Link} from 'expo-router';
import React from 'react';
import {Text, View} from 'react-native';
import {Image} from 'expo-image';

type Props = {
  element: any;
  type: 'course' | 'user';
  width?: number;
};

export default function ({element, width = undefined, type}: Props) {
  return (
    <Link
      style={tw`m-2`}
      href={
        type === 'course'
          ? appRoutes(`category_id=${element.id}`).courseIndex
          : appRoutes(`category_id=${element.id}`).userIndex
      }>
      <View style={tw` rounded-xl  items-center `}>
        <View style={tw`border  border-gray-200 self-center rounded-xl p-5.5 `}>
          <Image
            style={tw`h-7 w-7`}
            contentFit="contain"
            source={{uri: element.thumb}}
          />
        </View>
        <Text
          style={tw`text-black text-xs text-center font-semibold  capitalize mt-4 font-alfont`}>
          {element.name}
        </Text>
      </View>
    </Link>
  );
}
