import {AntDesign} from '@expo/vector-icons';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {appRoutes, imageSizes} from '@src/constants';
import {useGetTagsQuery} from '@src/redux/api/categoryApi';
import {AppQueryResult, Tag} from '@src/types';
import {Link} from 'expo-router';
import {isUndefined} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, VirtualizedList} from 'react-native';
import TagCard from './TagCard';

export default function ({
  query,
  type = 'document',
  showSeeAll = false,
}: any | void) {
  const {t} = useTranslation();
  const {
    locale: {isRTL},
  } = useAppSelector(state => state);
  const {
    data: elements,
    isFetching,
    isSuccess,
  } = useGetTagsQuery<{
    data: AppQueryResult<Tag[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(query ?? undefined);

  if (
    isFetching ||
    !isSuccess ||
    isUndefined(elements) ||
    !elements.data ||
    elements.data?.length == 0
  )
    return null;

  return (
    <View>
      {elements && elements.data && elements.data.length > 0 ? (
        <View style={tw`bg-white rounded-xl shadow-sm overflow-hidden`}>
          <View style={tw` flex-row justify-between items-center `}>
            <Text style={tw`font-bold text-xl font-alfont`}>{t('tags')}</Text>
            {showSeeAll && (
              <Link
                href={
                  type === 'document'
                    ? appRoutes(``).documentIndex
                    : appRoutes(``).questionnaireIndex
                }>
                <View
                  style={tw`flex flex-row justify-center items-center py-1 gap-x-2`}>
                  <Text style={tw`text-sm text-gray-500 font-alfont`}>
                    {t('see_all')}
                  </Text>
                  <AntDesign
                    style={tw` px-1 ${isRTL && `rotate-180`}`}
                    name={isRTL ? 'left' : 'right'}
                    size={imageSizes.xxs}
                    color={'gray'}
                  />
                </View>
              </Link>
            )}
          </View>
          {!isFetching && (
            <VirtualizedList
              data={elements.data}
              getItemCount={() => (elements.data ? elements.data.length : 0)}
              getItem={(data, index) => data[index]}
              renderItem={({item}: {item: Tag}) => (
                <TagCard element={item} key={item?.id} type={type} />
              )}
              keyExtractor={item => item.id.toString()}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              horizontal={true}
              initialNumToRender={elements?.data.length}
              contentContainerStyle={tw`py-2`}
              style={tw`w-full mx-3 overflow-hidden`}
              ItemSeparatorComponent={() => <View style={tw`mx-1`}></View>}
            />
          )}
        </View>
      ) : null}
    </View>
  );
}
