import React, {FC, useContext, useState, useEffect} from 'react';
import tw from '@root/tailwind';
import {ActivityIndicator, Pressable, Text, View} from 'react-native';
import {useTranslation} from 'react-i18next';
import {useAppSelector} from '@root/src/redux/hooks';
import {themeColors} from '@root/src/redux/slices/themeSlice';
import {imageSizes} from '@root/src/constants';
import {AntDesign} from '@expo/vector-icons';

type Props = {
  currentPage: number;
  lastPage: number;
  page: number | string;
  setPage: (page: number) => void;
};
const Pagination: FC<Props> = ({page, setPage, currentPage, lastPage}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const {primary, secondary} = useAppSelector(themeColors);
  const {t} = useTranslation();
  const {isRTL} = useAppSelector(state => state.locale);

  useEffect(() => {
    if (currentPage === page) {
      setIsLoading(false);
    } else {
      setIsLoading(true);
    }
  }, [currentPage]);

  if (lastPage === 1) {
    return <></>;
  }

  return (
    <View
      style={tw`bg-white rounded-xl p-3 py-4 shadow-md flex  flex-row ${
        currentPage && lastPage >= currentPage
          ? `justify-end`
          : 'justify-between'
      } items-center flex-row`}>
      <View style={tw`flex-row flex-1 items-center  justify-start `}>
        {currentPage && currentPage > 1 && (
          <>
            <Pressable
              onPress={() => setPage(currentPage - 1)}
              style={tw`relative left-2 w-8 py-0.5 bg-theme-600 border border-theme-600 rounded-tl-xl rounded-bl-xl  `}>
              <AntDesign
                name={isRTL ? `arrowright` : `arrowleft`}
                size={imageSizes.xs}
                style={tw`ml-2`}
                color={'white'}
              />
            </Pressable>
            <Pressable
              onPress={() => setPage(currentPage - 1)}
              style={tw`py-2 px-2.2   rounded-md w-[20]  flex justify-center items-center bg-theme-600`}>
              <Text style={tw`text-theme-600 text-xs text-white font-alfont`}>
                {t('previous')}
              </Text>
            </Pressable>
          </>
        )}
      </View>
      {isLoading && (
        <View
          style={tw`self-center flex-1 broder-4 justify-center items-center`}>
          <ActivityIndicator />
        </View>
      )}
      <View style={tw`flex-row flex-1 items-center  justify-end `}>
        {currentPage && lastPage >= currentPage && (
          <>
            <Pressable
              onPress={() => setPage(currentPage + 1)}
              style={tw`py-2 px-2.2   rounded-md w-[20]  flex justify-center items-center bg-theme-600`}>
              <Text style={tw`text-theme-600 text-xs text-white font-alfont`}>
                {t('next')}
              </Text>
            </Pressable>
            <Pressable
              onPress={() => setPage(currentPage + 1)}
              style={tw`relative right-2 w-8 py-0.5 bg-theme-600 border border-theme-600 rounded-tr-xl rounded-br-xl  `}>
              <AntDesign
                name={isRTL ? `arrowleft` : `arrowright`}
                size={imageSizes.xs}
                style={tw`ml-2`}
                color={'white'}
              />
            </Pressable>
          </>
        )}
      </View>
    </View>
  );
};

export default Pagination;
