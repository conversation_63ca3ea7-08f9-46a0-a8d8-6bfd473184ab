import {useNetInfo} from '@react-native-community/netinfo';
import {DefaultTheme, ThemeProvider} from '@react-navigation/native';
import i18n from '@root/i18n/config';
import OfflineWidget from '@root/src/components/loading/OfflineWidget';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import ToastContainer from '@root/src/components/ToastContainer';
import {store} from '@root/src/redux/store';
import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {useFonts} from 'expo-font';
import {Stack} from 'expo-router/stack';
import * as SplashScreen from 'expo-splash-screen';
import React, {useEffect} from 'react';
import {I18nextProvider} from 'react-i18next';
import {StatusBar, useColorScheme} from 'react-native';
import 'react-native-gesture-handler';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import 'react-native-reanimated';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import {
  DefaultTheme as PaperDefaultTheme,
  Provider as PaperProvider,
} from 'react-native-paper';
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'https://<EMAIL>/4508581183225936',

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // enableSpotlight: __DEV__,
});
SplashScreen.preventAutoHideAsync();

const theme = {
  ...PaperDefaultTheme,
  roundness: 2,
  colors: {
    ...DefaultTheme.colors,
    primary: tw.color(`text-theme-700`),
    accent: tw.color(`text-theme-200`),
    background: tw.color(`bg-white`),
  },
};

// Inner component that can use Redux hooks
function AppContent() {
  const {isConnected} = useNetInfo();
  const isLoading = useAppSelector(state => state.bootStrapped.isLoading);

  // Show loading screen if app is still bootstrapping
  if (isLoading) {
    return <MainLoadingView />;
  }

  return (
    <>
      <ToastContainer />
      <StatusBar
        animated={true}
        showHideTransition={'slide'}
        barStyle={'light-content'}
        backgroundColor={tw.color(`bg-gray-100`)}
      />
      {isConnected ? (
        <SafeAreaProvider style={tw`android:pt-[10%] ios:pt-[12%] bg-gray-100`}>
          <Stack
            initialRouteName="(tabs)"
            screenOptions={{
              headerShown: false,
            }}>
            <Stack.Screen
              name="(tabs)"
              options={{
                title: 'home 1',
                headerShown: false,
              }}
            />
          </Stack>
        </SafeAreaProvider>
      ) : (
        <OfflineWidget />
      )}
    </>
  );
}

export default function () {
  const colorScheme = useColorScheme();
  const [loaded, error] = useFonts({
    'font-alfont': require('./../assets/fonts/alfont_com_AlFont_com_ExpoArabic-SemiBold.ttf'),
    'font-expo': require('./../assets/fonts/ExpoArabic-Book.ttf'),
    'font-susi': require('./../assets/fonts/SuisseIntl-Medium.otf'),
    'font-space': require('./../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded || error) {
      SplashScreen.hideAsync();
    }
  }, [loaded, error]);

  return (
    <GestureHandlerRootView style={tw`flex-1`}>
      <ThemeProvider
        value={colorScheme === 'dark' ? DefaultTheme : DefaultTheme}>
        <PaperProvider theme={theme}>
          <Provider store={store}>
            <I18nextProvider i18n={i18n}>
              <AppContent />
            </I18nextProvider>
          </Provider>
        </PaperProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}
