import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {AppQueryResult, AppSetting, Slide, Tree} from '@root/src/types';
import {apiUrl, keepCache} from '@src/constants';
import {RootState} from '@src/redux/store';
import {isNull} from 'lodash';
import * as expoLocaliaztion from 'expo-localization';

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: `${apiUrl}`,
    timeout: 10000,
    prepareHeaders: async (headers, {getState}) => {
      const {
        auth,
        locale: {lang},
      } = getState() as RootState;
      headers.set('Accept-Language', 'en');
      headers.set('x-api-key', '83487382947328974');
      headers.set('Content-Type', 'application/json');
      headers.set('Accept', 'application/json');
      headers.set('Accept-Language', lang);
      if (auth.id !== 0 && !isNull(auth.api_token)) {
        headers.set('Authorization', `Bearer ${auth.api_token}`);
        headers.set('api_token', `${auth.api_token}`);
      }
      return headers;
    },
    credentials: 'include',
  }),

  // tagTypes: ['favoritesList'],
  keepUnusedDataFor: keepCache,
  refetchOnReconnect: true,
  refetchOnMountOrArgChange: true,
  refetchOnFocus: true,
  endpoints: builder => ({
    getSetting: builder.query<AppSetting, void>({
      query: () => `setting`,
    }),
    getSlides: builder.query<AppQueryResult<Slide[]>, void>({
      query: () => ({
        url: `slide`,
      }),
    }),
    getTrees: builder.query<AppQueryResult<Tree[]>, void>({
      query: () => ({
        url: `tree`,
      }),
    }),
    sendWhatsApp: builder.query<any, {mobile: string; content: string}>({
      query: () => ({
        url: `send/whatsapp`,
      }),
    }),
  }),
});

export const {
  useGetSettingQuery,
  useGetSlidesQuery,
  useGetTreesQuery,
  useSendWhatsAppQuery,
} = apiSlice;
