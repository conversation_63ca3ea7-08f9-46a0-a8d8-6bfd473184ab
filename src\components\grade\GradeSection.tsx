//import liraries
import {useGetGradesQuery} from '@root/src/redux/api/gradeApi';
import tw from '@root/tailwind';
import GradeCard from '@src/components/grade/GradeCard';
import {AppQueryResult, CategoriesType, Grade} from '@src/types';
import {isUndefined} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, VirtualizedList} from 'react-native';

export default function () {
  const {
    data: elements,
    isFetching,
    isSuccess,
  } = useGetGradesQuery<{
    data: AppQueryResult<Grade[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>({
    on_home: 1,
    order_by: 'asc',
  });

  const {t} = useTranslation();

  if (
    (isFetching && !isSuccess && isUndefined(elements)) ||
    !elements?.data ||
    elements.data?.length == 0
  )
    return null;

  return (
    <View style={tw`bg-white rounded-xl shadow-sm overflow-hidden`}>
      <View style={tw`p-3 flex-row justify-between items-center `}>
        <Text style={tw`font-bold font-alfont capitalize text-xl`}>
          {t('grade')}
        </Text>
        {/* <View style={tw`flex-row justify-start items-center`}>
          <Text style={tw`text-sm font-alfont capitalize text-gray-500`}>{t('see_all')}</Text>
          <AntDesign   name={isRTL ? 'left' : 'right'} size={imageSizes.xxs}   style={tw` px-1 ${isRTL && `rotate-180`}`} color={'gray'} />
        </View> */}
      </View>
      {!isFetching && (
        <VirtualizedList
          data={elements.data}
          getItemCount={() => (elements.data ? elements.data.length : 0)}
          getItem={(data, index) => data[index]}
          renderItem={({item}: {item: CategoriesType}) => (
            <GradeCard element={item} key={item?.id} />
          )}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          initialNumToRender={elements.data?.length}
          contentContainerStyle={tw``}
          style={tw`w-full mx-3`}
          ItemSeparatorComponent={() => <View style={tw`mx-0.5`}></View>}
        />
      )}
    </View>
  );
}
