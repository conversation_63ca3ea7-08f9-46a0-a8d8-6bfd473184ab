const {plugin} = require('twrnc');
const colors = require('tailwindcss/colors'); // Import default Tailwind CSS colors

module.exports = {
  mode: 'jit',
  theme: {
    // Spread the default colors into your custom configuration

    extend: {
      fontSize: {
        xs: '0.7rem',
        xxs: '0.2rem',
        md: '1rem', // Adding md size to fix the unknown utility warning
      },
      colors: {
        red: {
          900: '#d00000',
        },
        gray: {
          25: '#FCFCFD',
          50: '#F9FAFB',
          100: '#F2F4F7',
          200: '#EAECF0',
          300: '#D0D5DD',
          400: '#98A2B3',
          500: '#667085',
          600: '#475467',
          700: '#344054',
        },
        theme: {
          100: '#E8F6F3',
          200: '#C5E8E2',
          300: '#A2D9D0',
          400: '#7FCBBF',
          500: '#5CBDAD',
          600: '#45B4A1',
          700: '#16A18A',
          800: '#12816E',
          900: '#0B5145',
          extraLight: '#E8F6F3',
          light: '#A2D9D0',
          medium: '#45B4A1',
          default: '#5CBDAD',
          dark: '#12816E',
        },
        prim: {
          100: '#E6EDED',
          200: '#BFD2D2',
          300: '#99B6B6',
          400: '#739B9B',
          500: '#4D8080',
          600: '#266464',
          700: '#004949',
          800: '#003A3A',
          900: '#002828',
        },
      },
    },
    fontFamily: {
      'Tajawal-Medium': ['Tajawal-Medium', 'sans-serif'],
    },
  },
  variants: {
    opacity: ({after}) => after(['disabled']),
  },
  plugins: [
    plugin(({addUtilities}) => {
      addUtilities({
        '.font-alfont': {
          fontFamily: 'font-alfont',
        },
        '.font-expo': {
          fontFamily: 'font-expo',
        },
        '.font-susi': {
          fontFamily: 'font-susi',
        },
        '.font-space': {
          fontFamily: 'font-space',
        },
        '.leading-normal': {
          lineHeight: 18,
        },
        '.leading-loose': {
          lineHeight: 24,
        },
        '.card-icon': `h-5 w-5 text-theme-700`,
        '.flat-list-wrapper':
          'flex justify-center w-full self-center items-center gap-y-4 my-[2%] p-3 bg-white rounded-xl pb-20',
        '.btn-default':
          'bg-theme-700 p-4 rounded-3xl flex justify-center items-center text-center text-white',
        '.body-text': `text-gray-700 text-md text-left`,
      });
    }),
  ],
};
