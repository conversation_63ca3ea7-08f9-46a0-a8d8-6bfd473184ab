import { A } from '@expo/html-elements';
import { AntDesign, FontAwesome5 } from '@expo/vector-icons';
import PromoCode from '@root/src/components/cart/PromoCode';
import { appRoutes, imageSizes, whatsappUrl } from '@root/src/constants';
import { useGetSettingQuery } from '@root/src/redux/api/apiSlice';
import {
  useCreateOrderMutation,
  useUpdateOrderMutation,
} from '@root/src/redux/api/authApi';
import { useAppDispatch, useAppSelector } from '@root/src/redux/hooks';
import { getAuth, isAuthenticated } from '@root/src/redux/slices/authSlice';
import {
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import { AppSetting } from '@root/src/types';
import tw from '@root/tailwind';
import { useRouter } from 'expo-router';
import { ceil, random, round } from 'lodash';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ActivityIndicator,
  EmitterSubscription,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  finishTransaction,
  getProducts,
  initConnection,
  Product,
  purchaseErrorListener,
  purchaseUpdatedListener,
  requestPurchase,
  validateReceiptIos,
} from 'react-native-iap';
let purchaseUpdatedListenerObject: EmitterSubscription | undefined;
let purchaseErrorListenerObject: EmitterSubscription | undefined;
let transactionListenerObject: EmitterSubscription | null;

export default function () {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const auth = useAppSelector(getAuth);
  const cart = useAppSelector(state => state.cart);
  const isAuth = useAppSelector(isAuthenticated);
  const {
    locale: { isRTL },
    appSetting: { setting },
  } = useAppSelector(state => state);
  const [triggerCreateOrder] = useCreateOrderMutation();
  const [triggerUpdateOrder] = useUpdateOrderMutation();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [product, setProduct] = useState<Product>();
  const [orderID, setOrderID] = useState<string>();
  const [productLoading, setProductLoading] = useState<boolean>(
    Platform.OS === 'ios',
  );
  const [shownConvertedPrice, setShownConvertedPrice] = useState<
    number | string
  >();
  const createConvertedPricesList = (price: number) =>
    Array.from({ length: 11 }, (_, i) => `p_${price + i}`);
  const {
    data: settings,
    isFetching,
    isSuccess,
  } = useGetSettingQuery<{
    data: AppSetting;
    isSuccess: boolean;
    isFetching: boolean;
  }>();
  let orderId: any = undefined;
  let connectionInitated: boolean = false;

  const initIAP = async () => {
    try {
      const connection = await initConnection();
      return true;
    } catch (error) {
      await dispatch(
        showWarningToastMessage({ content: `IAP Initialization Error:${error}` }),
      );
      return false;
    }
  };

  useEffect(() => {
    if (Platform.OS === 'ios') {
      const initializeIOSIap = async () => {
        if (!connectionInitated) {
          purchaseUpdatedListenerObject?.remove();
          purchaseErrorListenerObject?.remove();
          connectionInitated = await initIAP();
        }
        if (connectionInitated) {
          //fetch the products from the apple iap matching the cart price
          if (!product) {
            const convertedPrice = ceil(
              round(cart?.net_total * settings.exchange_rate),
            );
            const productPriceIDs = createConvertedPricesList(convertedPrice);
            const foundProducts = await fetchProducts(productPriceIDs);
            if (foundProducts?.length) {
              const sortedProducts = foundProducts.sort(
                (a, b) => Number(a.price) - Number(b.price),
              );
              setProduct(sortedProducts[0]);
              setShownConvertedPrice(sortedProducts[0].price);

            } else {
              //do nothing already showed warning toast once in fetch product
              // await dispatch(showWarningToastMessage({ content: t("invalid_product") }));
            }
            setProductLoading(false);
          } else if (orderID) {
            //this listener listens to the initiated transactions, will listen when a "purchaseProduct" function is triggered
            //on success needs to update the created order in our db that was created using triggerUpdateOrder
            purchaseUpdatedListenerObject = purchaseUpdatedListener(
              async purchase => {
                // const samplePurchaseObject (this is what "purchase" object looks liike ) =
                // {
                //   "productId": "strive_course_190",
                //   "transactionDate": 1736748195000,
                //   "transactionId": "2000000828071876",
                //   "transactionReceipt": "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"
                // }

                // {"originalTransactionDateIOS": 1736754917000,
                //    "originalTransactionIdentifierIOS": "2000000828195737",
                //    "productId": "strive_course_190",
                //     "transactionDate": 1736754917000,
                //     "transactionId": "2000000828253516",
                //     "transactionReceipt":
                //     "MIIVnAYJKoZIhvcNAQcCoIIVjTCCFYkCAQExDzANBglghkgBZQMEAgEFADCCBNIGCSqGSIb3DQEHAaCCBMMEggS/MYIEuzAKAgEIAgEBBAIWADAKAgEUAgEBBAIMADALAgEBAgEBBAMCAQAwCwIBCwIBAQQDAgEAMAsCAQ8CAQEEAwIBADALAgEQAgEBBAMCAQAwCwIBGQIBAQQDAgEDMAwCAQMCAQEEBAwCMTAwDAIBCgIBAQQEFgI0KzAMAgEOAgEBBAQCAgFLMA0CAQ0CAQEEBQIDAr+FMA0CARMCAQEEBQwDMS4wMA4CAQkCAQEEBgIEUDMwNTAYAgEEAgECBBCcflr0AUnzOmDtpWT2LWDsMBsCAQACAQEEEwwRUHJvZHVjdGlvblNhbmRib3gwHAIBBQIBAQQU0QyrF4NQaF+0C++dYuZihoK4YaQwHgIBDAIBAQQWFhQyMDI1LTAxLTEzVDA4OjQ3OjMzWjAeAgESAgEBBBYWFDIwMTMtMDgtMDFUMDc6MDA6MDBaMCICAQICAQEEGgwYY29tLnBpeGVsdGVjaGt3LnN0cml2ZWR1MDcCAQcCAQEEL3YIaGL4rsUb1+KEXxd+yy4vnBKxT9m+Q09oYgSBbaQQqpEuON75ys6YibaIEDoJMFICAQYCAQEEShSLaMybqZqVahMAq/FSqamcfbw+YROIF8KXB4WegWVEvh+Dqa77zE6bhqm3rt3qycLJPkaqju76zhrhMuJNxr81WeFEAlhfOgIwMIIBWAIBEQIBAQSCAU4xggFKMAsCAgasAgEBBAIWADALAgIGrQIBAQQCDAAwCwICBrACAQEEAhYAMAsCAgayAgEBBAIMADALAgIGswIBAQQCDAAwCwICBrQCAQEEAgwAMAsCAga1AgEBBAIMADALAgIGtgIBAQQCDAAwDAICBqUCAQEEAwIBATAMAgIGqwIBAQQDAgEAMAwCAgauAgEBBAMCAQAwDAICBq8CAQEEAwIBADAMAgIGsQIBAQQDAgEAMAwCAga6AgEBBAMCAQAwEAICBqYCAQEEBwwFcDkuOTkwGwICBqcCAQEEEgwQMjAwMDAwMDgyODE4NzY5MzAbAgIGqQIBAQQSDBAyMDAwMDAwODI4MTg3NjkzMB8CAgaoAgEBBBYWFDIwMjUtMDEtMTNUMDc6NDc6NDZaMB8CAgaqAgEBBBYWFDIwMjUtMDEtMTNUMDc6NDc6NDZaMIIBZAIBEQIBAQSCAVoxggFWMAsCAgasAgEBBAIWADALAgIGrQIBAQQCDAAwCwICBrACAQEEAhYAMAsCAgayAgEBBAIMADALAgIGswIBAQQCDAAwCwICBrQCAQEEAgwAMAsCAga1AgEBBAIMADALAgIGtgIBAQQCDAAwDAICBqUCAQEEAwIBATAMAgIGqwIBAQQDAgEAMAwCAgauAgEBBAMCAQAwDAICBq8CAQEEAwIBADAMAgIGsQIBAQQDAgEAMAwCAga6AgEBBAMCAQAwGwICBqcCAQEEEgwQMjAwMDAwMDgyODE5NTczNzAbAgIGqQIBAQQSDBAyMDAwMDAwODI4MTk1NzM3MBwCAgamAgEBBBMMEXN0cml2ZV9jb3Vyc2VfMTkwMB8CAgaoAgEBBBYWFDIwMjUtMDEtMTNUMDc6NTU6MTdaMB8CAgaqAgEBBBYWFDIwMjUtMDEtMTNUMDc6NTU6MTdaoIIO4jCCBcYwggSuoAMCAQICEH05IAlOvvP478psEOqOQwMwDQYJKoZIhvcNAQELBQAwdTFEMEIGA1UEAww7QXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkxCzAJBgNVBAsMAkc1MRMwEQYDVQQKDApBcHBsZSBJbmMuMQswCQYDVQQGEwJVUzAeFw0yNDA3MjQxNDUwMDNaFw0yNjA4MjMxNDUwMDJaMIGJMTcwNQYDVQQDDC5NYWMgQXBwIFN0b3JlIGFuZCBpVHVuZXMgU3RvcmUgUmVjZWlwdCBTaWduaW5nMSwwKgYDVQQLDCNBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9uczETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCtDzabzzfagXFb1vEU/BnT9dTwN01cRsKaKUdRYb6xP5hZ7BwXuq+zCVcFRNcXbV3SMMK7M6HUifR2OVZXLTU/Tal4gtFaYdZ7sC6VVPAHv2DkKaQzPUevdo9dA5uaOAohzN8Ul4fUHWHKKo3EPlWufJ1iALAKGDm45h2N86Qw8ZSTY9sT6TyOKf3ViHOzFJhvc8niM9Un9rbjddbqzqvf4vgMvlmK7XB6rpIF2UwHIOVtTEh00D7+YHcBeT4TO3+FAM+Vf4JdlCA065J1tQZB+5+ZlyS677rYmUr0dy552Djeo9gvRVBE5DMimdX35ZyE+cYEEcvgVeE0yxWyIxWlAgMBAAGjggI7MIICNzAMBgNVHRMBAf8EAjAAMB8GA1UdIwQYMBaAFBmLl41KW2F4V/SlXDUSijkI47B1MHAGCCsGAQUFBwEBBGQwYjAtBggrBgEFBQcwAoYhaHR0cDovL2NlcnRzLmFwcGxlLmNvbS93d2RyZzUuZGVyMDEGCCsGAQUFBzABhiVodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHJnNTA1MIIBHwYDVR0gBIIBFjCCARIwggEOBgoqhkiG92NkBQYBMIH/MDcGCCsGAQUFBwIBFitodHRwczovL3d3dy5hcHBsZS5jb20vY2VydGlmaWNhdGVhdXRob3JpdHkvMIHDBggrBgEFBQcCAjCBtgyBs1JlbGlhbmNlIG9uIHRoaXMgY2VydGlmaWNhdGUgYnkgYW55IHBhcnR5IGFzc3VtZXMgYWNjZXB0YW5jZSBvZiB0aGUgdGhlbiBhcHBsaWNhYmxlIHN0YW5kYXJkIHRlcm1zIGFuZCBjb25kaXRpb25zIG9mIHVzZSwgY2VydGlmaWNhdGUgcG9saWN5IGFuZCBjZXJ0aWZpY2F0aW9uIHByYWN0aWNlIHN0YXRlbWVudHMuMDAGA1UdHwQpMCcwJaAjoCGGH2h0dHA6Ly9jcmwuYXBwbGUuY29tL3d3ZHJnNS5jcmwwHQYDVR0OBBYEFO8oV7RgiElVMfD9WA7x/RqTxCT8MA4GA1UdDwEB/wQEAwIHgDAQBgoqhkiG92NkBgsBBAIFADANBgkqhkiG9w0BAQsFAAOCAQEANSPSu1C/NmfMADVEfIqTp8Ren7lE6nJHzxCGuhztCnUeWTB1hcoidYlCC+GccOU+pTx6kPg/EqxzTCRYmS7fgfEPJaYOpTBOpeawzVN7RUuw5ls6MNa09CtSog9P1hMjgjPmLYWRUHwx1EhxlPoIle6dAGYaueaJDI6xiX0WSrCIFR0UKYcUHTH6rmoA8j2RY1uAgkgePkrTAt2GXc1y4jc8qAslu2Paqz8xZagnG/A7U0UdEn5GH8WsH8hznJj4NLBgfe7zEQxWlj4JBOft5B5HWbDwgzcu+xzHE6Npcuu9mCaQhI9uTfxoKftNbhjt3K2qucRpmBQI/flL+2z+mTCCBFUwggM9oAMCAQICFDt+gAru0wKh5uzbl9nKrCic8WmUMA0GCSqGSIb3DQEBCwUAMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTAeFw0yMDEyMTYxOTM4NTZaFw0zMDEyMTAwMDAwMDBaMHUxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MQswCQYDVQQLDAJHNTETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCfXdof+/q80EsiPMfWJvoX9/SfHj5kEWaa716+qzS9qiwhbtYelCGFLHTBDhBhqjxjSn5K48h11s/CnAhIe2q5KbHJZv3IihbRsgQ8grqAbOL/CnLrrP47b0i+nosRTZV9snuQLwIcTvxJvtdvtU++eMba3rLNydlmETta6QlFc4lQ1E7iaAV+2nWcSwGu2uPPbXRN3lPQ1Ro4gjrQneNdKXuxgeopJwv7YHyGEvvwYk8G50zRH9ltnu1z2nghDZ1w2UZXkF9nhMFzdwqoYmK2rnCGu3Ujia159uak1P2DJjIKOySSWyChnNEvgBib3TwL57X97IBXDxeePyuHJ7v3AgMBAAGjge8wgewwEgYDVR0TAQH/BAgwBgEB/wIBADAfBgNVHSMEGDAWgBQr0GlHlHYJ/vRrjS5ApvdHTX8IXjBEBggrBgEFBQcBAQQ4MDYwNAYIKwYBBQUHMAGGKGh0dHA6Ly9vY3NwLmFwcGxlLmNvbS9vY3NwMDMtYXBwbGVyb290Y2EwLgYDVR0fBCcwJTAjoCGgH4YdaHR0cDovL2NybC5hcHBsZS5jb20vcm9vdC5jcmwwHQYDVR0OBBYEFBmLl41KW2F4V/SlXDUSijkI47B1MA4GA1UdDwEB/wQEAwIBBjAQBgoqhkiG92NkBgIBBAIFADANBgkqhkiG9w0BAQsFAAOCAQEAWsQ1otnmCp5SogCCInfNci+Q+SKvFCXMqgpCYJLCvXUd60zKFeV+a0AQXvtbRXQN8Hp9iJHO3mOLQonSGN9Bs1ieBgiHSN1AryPV7essYOXrpH8c6ZyD1pRfTGI5ik6uE419Q7jcXqy+GEDy5g8sXROT8XtlqMJoSN7/tJabDPsyNp6eDZVfOAqLltISbLeLC47XPuxvAarOTUVg24RxZmLlGWUwzYr/RVP7bvuId0PDSGP591Gzcl554lbPvLuEuThaeK4RSFK7DTWLlN7MdJpo9UlglKzyqLMVhpDQzDBDhtPlcAJRtIHAqJfU6uqwjAlA7ziTss0iA+tnQ2XIRTCCBLswggOjoAMCAQICAQIwDQYJKoZIhvcNAQEFBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMB4XDTA2MDQyNTIxNDAzNloXDTM1MDIwOTIxNDAzNlowYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5JGpCR+R2x5HUOsF7V55hC3rNqJXTFXsixmJ3vlLbPUHqyIwAugYPvhQCdN/QaiY+dHKZpwkaxHQo7vkGyrDH5WeegykR4tb1BY3M8vED03OFGnRyRly9V0O1X9fm/IlA7pVj01dDfFkNSMVSxVZHbOU9/acns9QusFYUGePCLQg98usLCBvcLY/ATCMt0PPD5098ytJKBrI/s61uQ7ZXhzWyz21Oq30Dw4AkguxIRYudNU8DdtiFqujcZJHU1XBry9Bs/j743DN5qNMRX4fTGtQlkGJxHRiCxCDQYczioGxMFjsWgQyjGizjx3eZXP/Z15lvEnYdp8zFGWhd5TJLQIDAQABo4IBejCCAXYwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFCvQaUeUdgn+9GuNLkCm90dNfwheMB8GA1UdIwQYMBaAFCvQaUeUdgn+9GuNLkCm90dNfwheMIIBEQYDVR0gBIIBCDCCAQQwggEABgkqhkiG92NkBQEwgfIwKgYIKwYBBQUHAgEWHmh0dHBzOi8vd3d3LmFwcGxlLmNvbS9hcHBsZWNhLzCBwwYIKwYBBQUHAgIwgbYagbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjANBgkqhkiG9w0BAQUFAAOCAQEAXDaZTC14t+2Mm9zzd5vydtJ3ME/BH4WDhRuZPUc38qmbQI4s1LGQEti+9HOb7tJkD8t5TzTYoj75eP9ryAfsfTmDi1Mg0zjEsb+aTwpr/yv8WacFCXwXQFYRHnTTt4sjO0ej1W8k4uvRt3DfD0XhJ8rxbXjt57UXF6jcfiI1yiXV2Q/Wa9SiJCMR96Gsj3OBYMYbWwkvkrL4REjwYDieFfU9JmcgijNq9w2Cz97roy/5U2pbZMBjM3f3OgcsVuvaDyEO2rpzGU+12TZ/wYdV2aeZuTJC+9jVcZ5+oVK3G72TQiQSKscPHbZNnF5jyEuAF1CqitXa5PzQCQc3sHV1ITGCAbUwggGxAgEBMIGJMHUxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MQswCQYDVQQLDAJHNTETMBEGA1UECgwKQXBwbGUgSW5jLjELMAkGA1UEBhMCVVMCEH05IAlOvvP478psEOqOQwMwDQYJYIZIAWUDBAIBBQAwDQYJKoZIhvcNAQEBBQAEggEAUMUiAGlisU1vByDybThLVRSyO/DVHImLdSLwvRySBcINyBwZ6W0ekvn6WRJ5N/MSAdiyZxcD3JB3kcxuyJ2dXe3S+4rYooSqowPnaKAYum5r0LTw0/N+vYaDbNR6Sah2tl75sPrSDbrvazPwmjpD3VndcrDA89ICiVCZylSvvYkQIQcx/nZ+XCsprhEiHbmQNx12+4fNdcf2BKo//HWT+TnDaiQOaHaqpHIb7gtZE8n7F4MABL6b2TIw0FyYtrBszx5YmYlGPGaVq9DZ+ureEAc8uhXhdnB+lgP7keoS6OS2m+Y7mX2LI42bPrvWoYoiPzYV1sQP2RqV9v5vsQA1sw=="}
                const receipt = purchase?.transactionReceipt;
                const receiptBody = {
                  'receipt-data': receipt,
                  password: process.env.EXPO_APPLE_STORE_SHARED_SECRET,
                };
                const returnedReceipt = await validateReceiptIos({
                  receiptBody,
                  isTest: __DEV__,
                });
                if (returnedReceipt) {
                  const finishedTransaction = await finishTransaction({
                    purchase: purchase,
                    isConsumable: false,
                  });
                  if (finishedTransaction) {
                    console.log(
                      'finishedTransaction ======>>',
                      finishedTransaction,
                    );
                    if (!orderID) {
                      dispatch(
                        showWarningToastMessage({
                          content: t('purchase_success_order_update_error'),
                        }),
                      );
                    }

                    try {
                      //sample of returned receipt
                      // const returnedReceiptSample = {
                      //   "environment": "Sandbox",
                      //   "latest_receipt": "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",
                      //   "latest_receipt_info": [{
                      //     "in_app_ownership_type":
                      //       "PURCHASED",
                      //     "is_trial_period": "false",
                      //     "original_purchase_date": "2025-01-13 07:55:17 Etc/GMT",
                      //     "original_purchase_date_ms": "1736754917000",
                      //     "original_purchase_date_pst": "2025-01-12 23:55:17 America/Los_Angeles",
                      //     "original_transaction_id": "2000000828195737",
                      //     "product_id": "strive_course_190",
                      //     "purchase_date": "2025-01-13 07:55:17 Etc/GMT",
                      //     "purchase_date_ms": "1736754917000",
                      //     "purchase_date_pst": "2025-01-12 23:55:17 America/Los_Angeles",
                      //     "quantity": "1",
                      //     "transaction_id": "2000000828195737"
                      //   }, {
                      //     "in_app_ownership_type": "PURCHASED",
                      //     "is_trial_period": "false",
                      //     "original_purchase_date": "2025-01-13 07:47:46 Etc/GMT",
                      //     "original_purchase_date_ms": "1736754466000",
                      //     "original_purchase_date_pst": "2025-01-12 23:47:46 America/Los_Angeles",
                      //     "original_transaction_id": "2000000828187693",
                      //     "product_id": "p9.99",
                      //     "purchase_date": "2025-01-13 07:47:46 Etc/GMT",
                      //     "purchase_date_ms": "1736754466000",
                      //     "purchase_date_pst": "2025-01-12 23:47:46 America/Los_Angeles",
                      //     "quantity": "1",
                      //     "transaction_id": "2000000828187693"
                      //   }], "receipt": {
                      //     "adam_id": 0,
                      //     "app_item_id": 0,
                      //     "application_version": "10",
                      //     "bundle_id": "com.pixeltechkw.strivedu",
                      //     "download_id": 0, "in_app": [[Object], [Object]],
                      //     "original_application_version": "1.0",
                      //     "original_purchase_date": "2013-08-01 07:00:00 Etc/GMT",
                      //     "original_purchase_date_ms": "1375340400000",
                      //     "original_purchase_date_pst": "2013-08-01 00:00:00 America/Los_Angeles",
                      //     "receipt_creation_date": "2025-01-13 11:38:02 Etc/GMT",
                      //     "receipt_creation_date_ms": "1736768282000",
                      //     "receipt_creation_date_pst": "2025-01-13 03:38:02 America/Los_Angeles",
                      //     "receipt_type": "ProductionSandbox",
                      //     "request_date": "2025-01-13 11:38:04 Etc/GMT",
                      //     "request_date_ms": "1736768284499", "request_date_pst":
                      //       "2025-01-13 03:38:04 America/Los_Angeles", "version_external_identifier": 0
                      //   }, "status": 0
                      // }

                      await triggerUpdateOrder({
                        id: orderID,
                        paid: 1,
                        payment_method: 'ios',
                      }).then(r => {
                        if (r?.data) {
                          dispatch(
                            showSuccessToastMessage({
                              content: t('process_success'),
                            }),
                          );
                          router.replace({
                            pathname: appRoutes().paymentSuccess,
                          });
                          // purchaseUpdatedListenerObject?.remove();
                        } else {
                          dispatch(
                            showWarningToastMessage({
                              content: `${t(
                                'purchase_success_order_update_error',
                              )} ${r?.error ? r.error : 'unknown error'}`,
                            }),
                          );
                        }
                      });
                    } catch (e) {
                      setIsLoading(false);
                    }
                    setIsLoading(false);
                    // await purchaseErrorListenerObject?.remove()
                    // await purchaseUpdatedListenerObject?.remove()
                    // transactionListenerObject?.remove();
                  }
                }
              },
            );
            purchaseErrorListenerObject = purchaseErrorListener(async error => {
              if (error.responseCode === 2) {
                //do nothing, user cancellation , not a real error
              } else {
                await dispatch(
                  showWarningToastMessage({
                    content: `${t('checkout_error')}`,
                  }),
                );
              }
              setIsLoading(false);
            });
          }
        }
      };
      if (cart && cart?.net_total) {
        initializeIOSIap();
      }
    }
  }, [cart, orderID]);

  useEffect(() => {
    // onUnMount
    return () => purchaseUpdatedListenerObject?.remove();
  }, []);
  const handlePurchase = async (foundProduct: Product) => {
    try {
      const purchaseData = await requestPurchase({
        sku: foundProduct.productId,
        andDangerouslyFinishTransactionAutomaticallyIOS: false,
      });
    } catch (errr) {
      setIsLoading(false);
      await dispatch(
        showWarningToastMessage({
          content: `${t('handle_purchase_error')} ${errr}`,
        }),
      );
    }
  };

  const fetchProducts = async (productIDs: string[]) => {
    try {
      const fetchedProducts = await getProducts({ skus: productIDs }); // get product you want to check out , cannot get single product, returns list of "Product" (Product[]) .

      if (fetchedProducts.length > 0) {
        return fetchedProducts;
      } else {
        setIsLoading(false);
        await dispatch(
          showWarningToastMessage({ content: t('product_fetch_error') }),
        );
        return [];
      }
    } catch (error) {
      await dispatch(
        showWarningToastMessage({ content: t('product_fetch_error') }),
      );
      return [];
    }
  };

  const handleCreateOrder = async () => {
    //ios
    if (Platform.OS === 'ios') {
      //if product exists (price range on apple's iap exisits)
      if (product) {
        setIsLoading(true);
        //need to create order in our db regardless of ios
        triggerCreateOrder({
          elements: cart.cartElements,
          total: cart.total,
          discount: cart.discount,
          net_total: cart.net_total,
          user_id: auth.id,
          kind: cart.kind,
          coupon_id: cart.coupon?.id,
          payment_method: 'visa',
          reference_id: random(999, 99999),
          paid_at: moment().locale('en').format('YYYY-MM-DD HH:mm:ss'),
          paid: false,
          model_id: cart.element.id,
          model: cart.kind,
          //store which ios order id was used in notes
          notes: `Payment initiated through IAP (IOS), converted price value  = ${shownConvertedPrice} and IAP Product ID is ${product.productId} `,
        }).then(async (r: any) => {
          if (r.data && r.data.id) {
            orderId = await r?.data?.id;
            setOrderID(orderId);
            if (orderId) {
              await handlePurchase(product);
            } else {
              await dispatch(
                showWarningToastMessage({
                  content: t('invalid_product_id'),
                }),
              );
            }
          } else {
            setIsLoading(false);

            await dispatch(
              showWarningToastMessage({
                content: r?.error?.data?.message
                  ? r.error?.data?.message
                  : r.error?.data,
              }),
            );
          }
        });
      } else {
        //do nothing, already showing invalid product message
      }
    } else {
      //android
      await triggerCreateOrder({
        elements: cart.cartElements,
        total: cart.total,
        discount: cart.discount,
        net_total: cart.net_total,
        user_id: auth.id,
        kind: cart.kind,
        coupon_id: cart.coupon?.id,
        payment_method: 'knet',
        reference_id: random(999, 99999),
        paid_at: moment().locale('en').format('YYYY-MM-DD HH:mm:ss'),
        paid: false,
        model_id: cart.element.id,
        model: cart.kind,
      }).then((r: any) => {
        if (r.data && r.data.url) {
          dispatch(showSuccessToastMessage({ content: t('process_success') }));
          router.replace({
            pathname: appRoutes().payment,
            params: { url: r.data.url },
          });
        } else {
          dispatch(
            showWarningToastMessage({
              content: r?.error?.data?.message
                ? r.error?.data?.message
                : r.error?.data,
            }),
          );
        }
      });
    }
  };

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-col bg-white rounded-3xl p-3`}
      contentInset={{ bottom: 300 }}
      style={tw`rounded-xl m-3`}>
      <View style={tw`flex-row justify-start items-center pt-3 p-3 gap-x-1`}>
        <Text
          style={tw`font-alfont text-left text-lg capitalize text-black font-bold `}>
          {t('cart')}
        </Text>
        <Text
          style={tw`font-alfont text-lg capitalize  text-left text-black font-bold `}></Text>
      </View>
      <View style={tw` bg-gray-50 my-3 p-3 rounded-3xl `}>
        <View style={tw`flex-col  justify-between items-center gap-3`}>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('student_name')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-xs capitalize text-gray-500`}>
                {cart.user?.name}
              </Text>
            </View>
          </View>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('mobile')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                {cart.user?.mobile}
              </Text>
            </View>
          </View>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('email')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-xs capitalize text-gray-500`}>
                {cart.user?.email}
              </Text>
            </View>
          </View>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('type')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                {t(cart?.kind)}
              </Text>
            </View>
          </View>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('element_name')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                {cart.element?.name}
              </Text>
            </View>
          </View>
          <View style={tw`flex-row justify-between items-center`}>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                {t('no_of_elements')} :
              </Text>
            </View>
            <View style={tw`w-1/2`}>
              <Text
                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                {cart.cartElements?.length}
              </Text>
            </View>
          </View>
        </View>

        {cart.kind === 'course' && cart.element && (
          <View style={tw`flex-col justify-between py-3 items-center`}>
            <View style={tw`flex-row justify-start items-center`}>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('subject')} :
                </Text>
              </View>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {cart.element?.subject?.name}
                </Text>
              </View>
            </View>

            <View style={tw`flex-row justify-start items-center mt-3`}>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
                  {t('grade')} :
                </Text>
              </View>
              <View style={tw`w-1/2`}>
                <Text
                  style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
                  {cart.element?.subject?.grade?.name}
                </Text>
              </View>
            </View>
          </View>
        )}
      </View>
      {/* coupon */}
      {
        Platform.OS !== 'ios' &&
        <PromoCode />}

      <View style={tw` bg-gray-50 my-3  rounded-3xl`}>
        <Text
          style={tw`font-alfont text-left text-md capitalize text-black font-bold  p-3`}>
          {t('more_details')}
        </Text>
        <View style={tw` bg-white m-3 p-3 rounded-3xl `}>
          <View style={tw`flex-row justify-between items-center p-3`}>
            <Text
              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
              {t('total')}
            </Text>
            <Text
              style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
              {cart?.total}
            </Text>
          </View>

          <View style={tw`flex-row justify-between items-center p-3`}>
            <Text
              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
              {t('discount')}
            </Text>
            <Text
              style={tw`font-alfont text-left text-sm  capitalize text-red-500`}>
              {cart?.discount}
            </Text>
          </View>

          <View style={tw`border border-gray-100 border-b `}></View>
          <View style={tw`flex-row justify-between items-center  p-3`}>
            <Text
              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
              {t('net_total')}
            </Text>
            <Text
              style={tw`font-alfont text-left text-sm  capitalize text-red-500 uppercase`}>
              {cart?.net_total} {t('kd')}
            </Text>
          </View>
          {/* net_total in USD */}

          {Platform.OS === 'ios' && (
            <View
              style={tw`flex flex-col justify-between py-3 items-center w-full`}>
              {productLoading ? (
                <View
                  style={tw`self-center flex-1 broder-4 justify-center items-center`}>
                  <ActivityIndicator />
                </View>
              ) : shownConvertedPrice && product ? (
                <View>
                  <View
                    style={tw`flex-row justify-between items-center  p-3 w-full`}>
                    <Text
                      style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
                      {t('net_total_in_usd')}
                    </Text>
                    <Text
                      style={tw`font-alfont text-left text-sm  capitalize text-red-500 uppercase`}>
                      {`${shownConvertedPrice} ${t('usd')}`}
                      {/* {round(cart?.net_total * settings.exchange_rate)} {t('usd')} */}
                    </Text>
                  </View>
                  <View
                    style={tw`flex-row justify-between items-center  p-3 w-full `}>
                    <Text
                      style={tw`font-alfont text-start text-sm capitalize text-black font-bold w-full  `}>
                      {`* ${t('prices_may_defer_depending_on_bank_currency')}`}
                    </Text>
                  </View>
                </View>
              ) : (
                <View
                  style={tw`flex-row justify-between items-center  p-3 w-full `}>
                  <Text
                    style={tw`font-alfont text-start text-sm capitalize text-black font-bold w-full  `}>
                    {`* ${t('product_cannot_be_purchased')}`}
                  </Text>
                </View>
              )}
              {/* APPLE IAP USD disclaimar */}
            </View>
          )}
        </View>
      </View>

      {Platform.OS === 'ios' ? (

        productLoading ? <TouchableOpacity
          style={tw`btn-default mt-3 flex flex-row  bg-[black]`}
          disabled={true}>

          <View
            style={tw`self-center flex-1 broder-4 justify-center items-center`}>
            <ActivityIndicator />
          </View>
        </TouchableOpacity> :
          shownConvertedPrice && !productLoading && product ? (
            <TouchableOpacity
              style={tw`btn-default mt-3 flex flex-row ${(!isAuth && cart.total > 0) || !shownConvertedPrice
                ? 'bg-[gray]'
                : 'bg-[black]'
                }`}
              onPress={() => handleCreateOrder()}
              disabled={(!isAuth && cart.total > 0) || !shownConvertedPrice}>
              {isLoading ? (
                <View
                  style={tw`self-center flex-1 broder-4 justify-center items-center`}>
                  <ActivityIndicator />
                </View>
              ) : (
                <View
                  style={tw`self-center flex flex-row justify-center items-center`}>
                  <AntDesign size={16} color={'white'} name={'apple1'} />

                  <Text style={tw`font-alfont text-white ml-2`}>
                    {t('go_to_payment')}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
          ) : (
            <View
              style={tw`btn-default  mt-3 flex flex-row justify-center items-center w-full`}>
              <A
                style={tw`flex-row w-full`}
                href={whatsappUrl(
                  setting.whatsapp,
                  `${t('course')} : ${cart.element.name} - ${t('course_id')} : ${cart.element.id
                  }`,
                )}>
                <View style={tw`w-[80%]`}>
                  <Text style={tw`font-alfont text-white capitalize px-4`}>
                    {t('contact_customer_service_to_enroll')}
                  </Text>
                </View>
                <View>
                  <FontAwesome5
                    name="whatsapp"
                    color={tw.color(`white`)}
                    size={imageSizes.xs}
                    style={tw` ${isRTL && `rotate-180`} h-5 w-5 mx-4 `}
                  />
                </View>
              </A>
            </View>
          )
      ) : (
        <TouchableOpacity
          style={tw`btn-default mt-3`}
          onPress={() => handleCreateOrder()}
          disabled={!isAuth && cart.total > 0}>
          <Text style={tw`font-alfont text-white`}>{t('go_to_payment')}</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
}

//  <ScrollView
//    showsVerticalScrollIndicator={false}
//    contentContainerStyle={tw`flex flex-grow mx-auto w-full`}
//    contentInset={{bottom: 300}}
//    style={tw`rounded-xl m-3`}>
//    <View style={tw`flex flex-col bg-white  h-200 rounded-3xl p-3`}>
//      <View style={tw`flex-row justify-start items-center pt-3 p-3 gap-x-1`}>
//        <Text
//          style={tw`font-alfont text-left text-lg capitalize text-black font-bold `}>
//          {t('cart')}
//        </Text>
//        <Text
//          style={tw`font-alfont text-lg capitalize  text-left text-black font-bold `}></Text>
//      </View>
//      <View style={tw` bg-gray-50 my-3 p-3 rounded-3xl `}>
//        <View style={tw`flex-col  justify-between items-center gap-3`}>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('student_name')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-xs capitalize text-gray-500`}>
//                {cart.user?.name}
//              </Text>
//            </View>
//          </View>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('mobile')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {cart.user?.mobile}
//              </Text>
//            </View>
//          </View>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('email')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-xs capitalize text-gray-500`}>
//                {cart.user?.email}
//              </Text>
//            </View>
//          </View>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('type')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {t(cart?.kind)}
//              </Text>
//            </View>
//          </View>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('element_name')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {cart.element?.name}
//              </Text>
//            </View>
//          </View>
//          <View style={tw`flex-row justify-between items-center`}>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('no_of_elements')} :
//              </Text>
//            </View>
//            <View style={tw`w-1/2`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {cart.cartElements?.length}
//              </Text>
//            </View>
//          </View>
//        </View>

//        {cart.kind === 'course' && cart.element && (
//          <View style={tw`flex-row justify-between py-3 items-center`}>
//            <View style={tw`flex-row justify-start mx-1 items-center`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('subject')} :
//              </Text>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {cart.element?.subject?.name}
//              </Text>
//            </View>
//            <View style={tw`flex-row justify-start items-center`}>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-black font-bold`}>
//                {t('grade')} :
//              </Text>
//              <Text
//                style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//                {cart.element?.subject?.grade?.name}
//              </Text>
//            </View>
//          </View>
//        )}
//      </View>

//      {/* coupon */}
//      <PromoCode />

//      <View style={tw` bg-gray-50 my-3  rounded-3xl`}>
//        <Text
//          style={tw`font-alfont text-left text-md capitalize text-black font-bold  p-3`}>
//          {t('more_details')}
//        </Text>
//        <View style={tw` bg-white m-3 p-3 rounded-3xl `}>
//          <View style={tw`flex-row justify-between items-center p-3`}>
//            <Text
//              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
//              {t('total')}
//            </Text>
//            <Text
//              style={tw`font-alfont text-left text-sm capitalize text-gray-500`}>
//              {cart?.total}
//            </Text>
//          </View>

//          <View style={tw`flex-row justify-between items-center p-3`}>
//            <Text
//              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
//              {t('discount')}
//            </Text>
//            <Text
//              style={tw`font-alfont text-left text-sm  capitalize text-red-500`}>
//              {cart?.discount}
//            </Text>
//          </View>

//          <View style={tw`border border-gray-100 border-b `}></View>
//          <View style={tw`flex-row justify-between items-center  p-3`}>
//            <Text
//              style={tw`font-alfont text-left text-sm capitalize text-black font-bold `}>
//              {t('net_total')}
//            </Text>
//            <Text
//              style={tw`font-alfont text-left text-sm  capitalize text-red-500`}>
//              {cart?.net_total}
//            </Text>
//          </View>
//        </View>
//      </View>

//      <TouchableOpacity
//        style={tw`btn-default mt-3`}
//        onPress={() => handleCreateOrder()}
//        disabled={!isAuth && cart.total > 0}>
//        <Text style={tw`font-alfont text-white`}>{t('go_to_payment')}</Text>
//      </TouchableOpacity>
//    </View>
//  </ScrollView>;
