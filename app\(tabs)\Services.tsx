import {A} from '@expo/html-elements';
import {Entypo} from '@expo/vector-icons';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import {baseUrl, imageSizes} from '@root/src/constants';
import {useGetTreesQuery} from '@root/src/redux/api/apiSlice';
import {AppQueryResult, Tree} from '@root/src/types';
import tw from '@root/tailwind';
import {Image} from 'expo-image';
import {useLocalSearchParams} from 'expo-router';
import {includes, map} from 'lodash';
import {RefreshControl, ScrollView, Text, View} from 'react-native';
import {List} from 'react-native-paper';

export default function () {
  const query: [{[key: string]: string | number}] | any = useLocalSearchParams<{
    query?: string | [];
  }>();
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetTreesQuery<{
    data: AppQueryResult<Tree[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(query);

  if (isFetching) return <MainLoadingView />;

  return (
    <ScrollView
      style={tw`flex-1 mx-3 rounded-xl`}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex-grow `}
      contentInset={{bottom: 100}}
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }>
      <View style={tw`w-full gap-x-4 px-3`}>
        {map(elements, (t: Tree, i: number) => (
          <View key={i} style={tw`rounded-xl bg-white my-2 px-2`}>
            <List.Section style={tw`bg-white rounded-xl `}>
              <List.Accordion
                title={t.name}
                left={() => (
                  <View>
                    <Image
                      source={{uri: t.thumb}}
                      style={tw`w-10 h-10 rounded-xl`}
                      contentFit="cover"
                    />
                  </View>
                )}
                right={() => (
                  <View>
                    <A href={t.url}>
                      <Entypo
                        name={`link`}
                        size={imageSizes.xs}
                        style={tw`${
                          t.color ? `text-[${t.color}]` : `text-theme-800`
                        } p-1 border border-gray-200 rounded-xl`}
                      />
                    </A>
                  </View>
                )}
                // description={'this is a test'}
                id={t.id}
                background={{
                  color: 'white',
                  radius: 20,
                  borderless: false,
                  foreground: false,
                }}
                rippleColor={tw.color('white')}
                titleStyle={tw`font-alfont`}
                style={tw`bg-white rounded-xl flex justify-start items-center`}>
                {map(t.trees, (t1, i) => (
                  <View key={i} style={tw`w-full p-3`}>
                    <A href={t1.url ?? `${baseUrl}`}>
                      <View
                        style={tw`flex flex-row justify-between items-center  w-full`}>
                        <View style={tw`flex flex-1`}>
                          <Text
                            style={tw`font-alfont text-left text-[${t1.color}]`}>
                            {t1.name}
                          </Text>
                        </View>
                        {includes(t1.url, 'http') && (
                          <View>
                            <Entypo
                              name={`link`}
                              size={imageSizes.xs}
                              style={tw`text-[${t1.color}] p-1 border border-gray-200 rounded-xl`}
                            />
                          </View>
                        )}
                      </View>
                    </A>
                    {t1.children &&
                      map(t1.children, (t2, i) => (
                        <View key={i} style={tw`w-full m-3 p-3`}>
                          <A href={t2.url ?? `${baseUrl}`}>
                            <View
                              style={tw`flex flex-row justify-between items-center  w-full`}>
                              <View style={tw`flex flex-1`}>
                                <Text
                                  style={tw`font-alfont text-left text-[${t2.color}]`}>
                                  {t2.name}
                                </Text>
                              </View>
                              {includes(t2.url, 'http') && (
                                <View>
                                  <Entypo
                                    name={`link`}
                                    size={imageSizes.xs}
                                    style={tw`text-[${t2.color}] p-1 border border-gray-200 rounded-xl`}
                                  />
                                </View>
                              )}
                            </View>
                          </A>

                          {t2.children &&
                            t2.children.length > 0 &&
                            map(t2.children, (t3, i) => (
                              <View key={i} style={tw`w-full m-3 p-3`}>
                                <A href={t3.url ?? `${baseUrl}`}>
                                  <View
                                    style={tw`flex flex-row justify-between items-center  w-full`}>
                                    <View style={tw`flex flex-1`}>
                                      <Text
                                        style={tw`font-alfont text-left text-[${t3.color}]`}>
                                        {t3.name}
                                      </Text>
                                    </View>
                                    {includes(t3.url, 'http') && (
                                      <View>
                                        <Entypo
                                          name={`link`}
                                          size={imageSizes.xs}
                                          style={tw`text-[${t3.color}] p-1 border border-gray-200 rounded-xl`}
                                        />
                                      </View>
                                    )}
                                  </View>
                                </A>
                                {t3.children &&
                                  t3.children.length > 0 &&
                                  map(t3.children, (t4, i) => (
                                    <View key={i} style={tw`w-full m-3 p-3`}>
                                      <A href={t4.url ?? `${baseUrl}`}>
                                        <View
                                          style={tw`flex flex-row justify-between items-center  w-full`}>
                                          <View style={tw`flex flex-1`}>
                                            <Text
                                              style={tw`font-alfont text-left text-[${t4.color}]`}>
                                              {t4.name}
                                            </Text>
                                          </View>
                                          {includes(t4.url, 'http') && (
                                            <View>
                                              <Entypo
                                                name={`link`}
                                                size={imageSizes.xs}
                                                style={tw`text-[${t4.color}] p-1 border border-gray-200 rounded-xl`}
                                              />
                                            </View>
                                          )}
                                        </View>
                                      </A>
                                      {t4.children &&
                                        t3.children.length > 0 &&
                                        map(t4.children, (t5, i) => (
                                          <A href={t5.url ?? baseUrl} key={i}>
                                            <View
                                              style={tw`flex flex-row justify-between items-center  w-full`}>
                                              <View style={tw`flex flex-1`}>
                                                <Text
                                                  style={tw`font-alfont text-left text-[${t5.color}]`}>
                                                  {t5.name}
                                                </Text>
                                              </View>
                                              {includes(t5.url, 'http') && (
                                                <View>
                                                  <Entypo
                                                    name={`link`}
                                                    size={imageSizes.xs}
                                                    style={tw`text-[${t5.color}] p-1 border border-gray-200 rounded-xl`}
                                                  />
                                                </View>
                                              )}
                                            </View>
                                          </A>
                                        ))}
                                    </View>
                                  ))}
                              </View>
                            ))}
                        </View>
                      ))}
                  </View>
                ))}
              </List.Accordion>
            </List.Section>
          </View>
        ))}
      </View>
    </ScrollView>
  );
}
