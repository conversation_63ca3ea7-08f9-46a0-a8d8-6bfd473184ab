//import liraries
import {AntDesign} from '@expo/vector-icons';
import {useGetUsersQuery} from '@root/src/redux/api/userApi';
import {useAppSelector} from '@root/src/redux/hooks';
import {AppQueryResult, User} from '@root/src/types';
import tw from '@root/tailwind';
import UserCard from '@src/components/user/UserCard';
import {appRoutes, imageSizes} from '@src/constants';
import {Link} from 'expo-router';
import {isUndefined} from 'lodash';
import React from 'react';
import {useTranslation} from 'react-i18next';
import {Text, View, VirtualizedList} from 'react-native';

export default function ({query}: any | void) {
  const {
    data: elements,
    isFetching,
    isSuccess,
  } = useGetUsersQuery<{
    data: AppQueryResult<User[]>;
    isSuccess: boolean;
    isFetching: boolean;
  }>(query ?? undefined);
  const {
    locale: {isRTL},
  } = useAppSelector(state => state);
  const {t} = useTranslation();

  if (
    isFetching ||
    !isSuccess ||
    isUndefined(elements) ||
    !elements?.data ||
    elements.data?.length == 0
  )
    return null;

  return (
    <View style={tw`bg-white rounded-xl shadow-sm my-1 overflow-hidden`}>
      {elements && elements.data.length > 0 && (
        <View style={tw`p-3 flex-row justify-between items-center `}>
          <Text style={tw`font-bold font-alfont capitalize text-xl`}>
            {t('success_partners')}
          </Text>
          <Link href={appRoutes(``).userIndex}>
            <View style={tw`flex-row justify-start items-center`}>
              <Text style={tw`text-sm font-alfont capitalize text-gray-500`}>
                {t('see_all')}
              </Text>
              <AntDesign
                name={isRTL ? 'left' : 'right'}
                size={imageSizes.xxs}
                style={tw` px-1 ${isRTL && `rotate-180`}`}
                color={'gray'}
              />
            </View>
          </Link>
        </View>
      )}

      {elements && elements.data && !isFetching && (
        <VirtualizedList
          data={elements.data}
          getItemCount={() => (elements.data ? elements.data.length : 0)}
          getItem={(data, index) => data[index]}
          renderItem={({item}: {item: User}) => (
            <UserCard width={55} element={item} key={item?.id} />
          )}
          keyExtractor={item => item.id.toString()}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          horizontal={true}
          initialNumToRender={elements.data?.length}
          contentContainerStyle={tw`overflow-hidden`}
          style={tw`w-full mx-3 overflow-hidden`}
          ItemSeparatorComponent={() => <View style={tw` mx-2 `}></View>}
        />
      )}
    </View>
  );
}

//make this component available to the app
