import {AntDesign, EvilIcons} from '@expo/vector-icons';
import {DrawerActions} from '@react-navigation/native';
import tw from '@root/tailwind';
import {
  appRoutes,
  icons,
  imageSizes,
  images,
  searchModules,
} from '@src/constants';
import {useAppDispatch, useAppSelector} from '@src/redux/hooks';
import {setSearchModule} from '@src/redux/slices/appSettingSlice';
import {isAuthenticated} from '@src/redux/slices/authSlice';
import {Image} from 'expo-image';
import {Link, useNavigation} from 'expo-router';
import {filter, isEmpty, isUndefined, map} from 'lodash';
import {Fragment, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {Pressable, Text, TextInput, TouchableOpacity, View} from 'react-native';
import {Badge, Modal, Portal} from 'react-native-paper';

export default function () {
  const navigation = useNavigation();
  const dispatch = useAppDispatch();
  const [visible, setVisible] = useState<boolean>(false);
  const [search, setSearch] = useState<string>(``);
  const showModal = () => setVisible(true);
  const hideModal = () => setVisible(false);
  const {
    cart: {total},
    locale: {isRTL},
    appSetting: {searchModule, setting},
  } = useAppSelector(state => state);
  const isAuth = useAppSelector(isAuthenticated);
  const {t} = useTranslation();

  const handleSelectSearchModule = (s: any) => {
    dispatch(setSearchModule(s));
    hideModal();
  };

  return (
    <Fragment>
      <View
        style={tw`flex flex-row w-full justify-between items-start w-full gap-1  h-32 `}>
        {/* search an logo */}
        <View style={tw`flex flex-col w-2/3 justify-start items-start h-32 `}>
          {/* img logo */}
          <View style={tw` h-18 flex justify-start`}>
            {!isUndefined(setting) && (
              <Image
                source={images.logo}
                style={tw`h-16 w-16  relative`}
                contentFit="contain"
              />
            )}
          </View>

          {/* input */}
          <View
            style={tw`flex-row justify-center items-center bg-white  h-12 rounded-xl shadow-sm px-2 w-full`}>
            <AntDesign
              name="search1"
              size={imageSizes.xs}
              style={tw` ${isRTL && `rotate-180`}`}
              color={tw.color(` text-gray-500 `)}
            />
            <TextInput
              placeholder={`${t('search')} ${
                !isEmpty(searchModule) ? ` (${t(searchModule.name)})` : ``
              }`}
              placeholderTextColor="gray"
              textAlign={isRTL ? 'right' : 'left'}
              style={tw`px-2 flex flex-1 capitalize font-alfont`}
              multiline={false}
              onChangeText={e => setSearch(e)}
            />
          </View>
        </View>

        {/* right icons */}
        <View
          style={tw`flex flex-row w-auto  justify-start items-center h-32 gap-2`}>
          {/* first icons col */}
          <View style={tw`flex flex-col gap-y-2`}>
            {/* cart icon and profile */}
            <View style={tw`w-12 h-12`}>
              {isAuth && total > 0 ? (
                <Link href={appRoutes().cart}>
                  <View
                    style={tw` h-12 w-12  bg-white rounded-xl justify-center items-center border border-gray-50 relative`}>
                    <Badge
                      style={tw`absolute -top-2 -right-2 bg-red-900 text-white z-50`}>
                      1
                    </Badge>

                    <EvilIcons
                      name="cart"
                      size={imageSizes.xs}
                      style={tw`card-icon ${isRTL && `rotate-180`} pt-1`}
                      color={tw.color(`theme-700`)}
                    />
                  </View>
                </Link>
              ) : (
                <Link href={appRoutes().profile}>
                  <View
                    style={tw` h-12 w-12  bg-white rounded-xl justify-center items-center border border-gray-50`}>
                    <icons.account
                      style={tw`card-icon ${isRTL && `rotate-180`} p-1`}
                    />
                  </View>
                </Link>
              )}
            </View>

            {/* filter icon */}
            <View style={tw`w-12 h-12    `}>
              <TouchableOpacity
                onPress={() => showModal()}
                style={tw` h-12 w-12 bg-white rounded-xl justify-center items-center border border-gray-50`}>
                <icons.homeSettings
                  style={tw`card-icon ${isRTL && `rotate-180`}  h-10 w-10`}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={tw`flex flex-col gap-y-2`}>
            {/* side menu */}
            <View style={tw`w-12 h-12  `}>
              <TouchableOpacity
                onPress={() => {
                  navigation.dispatch(DrawerActions.openDrawer());
                }}
                style={tw`bg-white rounded-xl justify-center items-center border border-gray-50 h-12 w-12 ios:bottom-1`}>
                <icons.Sidebar
                  style={tw`card-icon bg-white ${
                    isRTL && `rotate-180`
                  } h-10 w-10 rounded-xl`}
                />
              </TouchableOpacity>
            </View>

            {/* arrow  */}
            <View style={tw`w-12 h-12 `}>
              <Link
                href={`${
                  appRoutes(
                    `${
                      searchModule?.query?.group
                        ? `group=${searchModule.query.group}`
                        : ``
                    }&${
                      search && search.length > 2 ? `search=${search}` : ``
                    }&page=1`,
                  ).courseIndex
                }`}>
                <View
                  style={tw` h-12 w-12 bg-white rounded-xl justify-center items-center border border-gray-50`}>
                  <AntDesign
                    name={isRTL ? 'arrowleft' : 'arrowright'}
                    size={imageSizes.xs}
                    style={tw`${isRTL && `rotate-180`} pt-1`}
                    color={tw.color(` text-theme-700`)}
                  />
                </View>
              </Link>
            </View>
          </View>
        </View>
      </View>

      <Portal>
        <Modal
          visible={visible}
          onDismiss={hideModal}
          contentContainerStyle={tw`flex flex-col h-1/4 mx-6  rounded-2xl bg-white justify-start items-start border border-gray-300 `}>
          <View style={tw`w-full flex-row justify-center items-center`}>
            <Pressable
              hitSlop={100}
              onPress={() => hideModal()}
              style={tw`absolute top-4 right-4`}>
              <AntDesign name="close" style={tw`text-gray-400 w-8 h-8`} />
            </Pressable>
            <View style={tw`p-3`}>
              <Text style={tw`font-alfont text-lg text-gray-800 capitalize`}>
                {t('choose_search_type')}
              </Text>
            </View>
          </View>
          <View style={tw`w-full`}>
            {map(filter(searchModules, 'showFront'), (s, i) => (
              <Pressable
                onPress={() => handleSelectSearchModule(s)}
                key={s.name}
                style={tw`flex justify-center items-start w-full ${
                  i + 1 < 3 ? `border-b border-gray-200` : ``
                } p-3`}>
                <Text
                  style={tw`font-alfont ${
                    s.id === searchModule.id
                      ? `text-theme-700`
                      : `text-gray-800`
                  } text-left capitalize`}>
                  {t(s.name)}
                </Text>
              </Pressable>
            ))}
          </View>
        </Modal>
      </Portal>
    </Fragment>
  );
}
