import {AppQueryResult, Doc} from '@root/src/types';
import {apiSlice} from '@src/redux/api/apiSlice';
import {isUndefined} from 'lodash';

export const documentApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDocs: builder.query<
      AppQueryResult<Doc[]>,
      {[key: string]: string | number} | void
    >({
      query: params => {
        return {
          url: `document`,
          ...(!isUndefined(params) ? {params: {...params}} : {}),
        };
      },
    }),
    getDoc: builder.query<AppQueryResult<Doc>, string | number | undefined>({
      query: id => {
        return {
          url: `document/${id}`,
        };
      },
    }),
    getDocWithTag: builder.query<
      AppQueryResult<Doc[]>,
      {tagId: string | number | undefined}
    >({
      query: ({tagId}) => {
        return {
          url: `document?tag_id=${tagId}`,
        };
      },
    }),
  }),
});

export const {useGetDocsQuery, useGetDocQuery, useGetDocWithTagQuery} =
  documentApi;
