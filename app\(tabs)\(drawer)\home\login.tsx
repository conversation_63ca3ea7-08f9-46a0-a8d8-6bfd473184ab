import {A} from '@expo/html-elements';
import {Entypo} from '@expo/vector-icons';
import {yupResolver} from '@hookform/resolvers/yup';
import {appRoutes, baseUrl, icons, imageSizes} from '@root/src/constants';
import {usePostAuthMutation} from '@root/src/redux/api/authApi';
import {useAppDispatch, useAppSelector} from '@root/src/redux/hooks';
import {login, logout} from '@root/src/redux/slices/authSlice';
import {
  showSuccessToastMessage,
  showWarningToastMessage,
} from '@root/src/redux/slices/toastMessageSlice';
import {loginSchema} from '@root/src/validations';
import tw from '@root/tailwind';
import {Link, useNavigation, useRouter} from 'expo-router';
import {startCase} from 'lodash';
import {useState} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {useTranslation} from 'react-i18next';
import {
  Pressable,
  RefreshControl,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

export default function () {
  const {t} = useTranslation();
  const [triggerPostAuth] = usePostAuthMutation();
  const {lang, isRTL} = useAppSelector(state => state.locale);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [isChecked, setChecked] = useState(false);
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(true);
  const [ShowIcon, setShowIcon] = useState(false);
  const [loginError, setloginError] = useState('');
  const navigation = useNavigation();
  const {
    handleSubmit,
    control,
    formState: {errors, isValid, touchedFields},
    data,
  }: any = useForm({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      // login: `<EMAIL>`,
      // password: `password`,
      login: ``,
      password: ``,
      remember: true,
    },
  });

  const onSubmit = async (body: any) => {
    await triggerPostAuth({
      login: body.login,
      password: body.password,
      remember: true,
    }).then((r: any) => {
      if (r.data) {
        dispatch(login(r.data));
        dispatch(showSuccessToastMessage({content: t('process_success')}));
        router.dismissAll();
      } else {
        dispatch(showWarningToastMessage({content: r.error?.data?.message}));
        dispatch(logout(undefined));
      }
    });
  };

  return (
    <KeyboardAwareScrollView
      enableOnAndroid={true}
      keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
      scrollEnabled={true}
      extraScrollHeight={140}
      extraHeight={150}
      showsHorizontalScrollIndicator={false}
      horizontal={false}
      style={tw`flex  min-h-screen m-3 rounded-xl `}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex pb-26 bg-white px-3 rounded-xl w-full`}
      refreshControl={
        <RefreshControl refreshing={false} tintColor={tw.color(`theme-700`)} />
      }>
      <icons.login_logo
        color={tw.color(`gray-500`)}
        style={tw` ${
          isRTL && `rotate-180`
        } mt-[5%] self-center justify-center h-6 w-6`}
      />
      <View style={tw`flex-col pt-[10%]`}>
        <Text
          style={tw`font-alfont font-bold text-lg text-center text-black capitalize`}>
          {t('welcome')} !
        </Text>
        <Text
          style={tw`font-alfont font-bold text-md text-center text-gray-400 capitalize`}>
          {t('login_text')} !
        </Text>
      </View>
      <View style={tw`flex-col pt-5 `}>
        <Text
          style={tw`font-alfont font-bold text-md text-left px-1  text-gray-500 capitalize`}>
          {t('email_or_mobile_number')}
        </Text>
        <View style={tw`pt-3 px-1`}>
          <Controller
            control={control}
            render={({field: {onChange, onBlur, value}}) => (
              <View style={tw`relative`}>
                <TextInput
                  placeholder={`${t('enter_email_or_mobile_number')}`}
                  placeholderTextColor="gray-400"
                  onChangeText={value => {
                    onChange(value);
                  }}
                  onBlur={onBlur}
                  value={value}
                  style={tw`w-full border p-4 text-black h-13  font-alfont font-bold  rounded-xl border-gray-300 pl-5 ${
                    isRTL ? 'text-right' : `text-left`
                  }`}
                  secureTextEntry={false}
                  multiline={false}
                />
              </View>
            )}
            name="login"
          />
          <View>
            {errors?.login?.message?.key ? (
              <Text style={tw`body-text text-red-800 text-xs py-2 font-alfont`}>
                {startCase(
                  `${t(`${errors?.login?.message?.key}`, {
                    min: errors?.login?.message?.values,
                  })}`,
                )}
              </Text>
            ) : (
              <Text style={tw`body-text text-red-800 text-xs py-2 font-alfont`}>
                {startCase(`${t(errors?.login?.message)}`)}
              </Text>
            )}
          </View>
        </View>
        <View style={tw``}>
          <Text
            style={tw`font-alfont text-left px-1  font-bold text-md text-gray-500 capitalize`}>
            {t('password')}
          </Text>
          <View style={tw`pt-3 px-1 `}>
            <TouchableOpacity
              onPress={() => setPasswordVisible(!passwordVisible)}
              style={tw`absolute right-0.1 top-4 z-40`}>
              <Entypo
                name={passwordVisible ? 'eye-with-line' : 'eye'}
                size={imageSizes.xs}
                style={tw` absolute text-gray-400 right-4 top-4`}
              />
            </TouchableOpacity>
            <Controller
              control={control}
              render={({field: {onChange, onBlur, value}}) => (
                <View style={tw`relative  w-full `}>
                  <TextInput
                    placeholder={`${t('enter_password')}`}
                    placeholderTextColor="gray-400"
                    onChangeText={value => onChange(value)}
                    onBlur={onBlur}
                    value={value}
                    style={tw`w-full border p-4 text-black h-13  font-alfont font-bold  rounded-xl border-gray-300 pl-5 ${
                      isRTL ? 'text-right' : `text-left`
                    }`}
                    secureTextEntry={passwordVisible}
                    multiline={false}
                  />
                </View>
              )}
              name="password"
            />
            {errors.password && (
              <Text
                style={tw` text-left px-1  font-alfont capitalize text-red-800 text-xs py-3`}>
                * {t(`${errors?.password?.message}`)}
              </Text>
            )}

            {/* ForGot Password */}
            <View style={tw`flex-row justify-end items-center mt-[6%]`}>
              <A href={`${baseUrl}/${lang}/forgot-password`}>
                <Text style={tw`font-alfont text-theme-800 font-bold text-xs`}>
                  {t(`forgot_your_password`)}
                </Text>
              </A>
            </View>
          </View>
        </View>
      </View>

      <TouchableOpacity
        style={tw`btn-default bg-theme-800  mt-[7%]`}
        onPress={handleSubmit(onSubmit)}>
        <Text style={tw`font-alfont capitalize text-white text-md`}>
          {t('login')}
        </Text>
      </TouchableOpacity>

      <View>
        <View style={tw`pt-[9%] flex flex-row items-center self-center`}>
          <Pressable>
            <Text
              style={tw` text-xs text-center font-alfont capitalize text-gray-400  mx-1 font-bold   text-gray-400 `}>
              {t(`dont_have_account`)}
            </Text>
          </Pressable>
          <Pressable style={tw``}>
            <Link href={appRoutes(``).register}>
              <Text
                style={tw` text-sm text-theme-800 font-alfont capitalize text-center   `}>
                {t(`register here`)}
              </Text>
              <View style={tw`border-[0.25px]  w-full self-center `}></View>
            </Link>
          </Pressable>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
}
