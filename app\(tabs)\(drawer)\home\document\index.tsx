import DocumentCard from '@root/src/components/document/DocumentCard';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import NoResults from '@root/src/components/NoResults';
import Pagination from '@root/src/components/Pagination';
import TagSection from '@root/src/components/tag/TagSection';
import {useGetDocsQuery} from '@root/src/redux/api/documentApi';
import {AppQueryResult, Doc} from '@root/src/types';
import tw from '@root/tailwind';
import TransText from '@src/components/TransText';
import {useLocalSearchParams} from 'expo-router';
import {isEmpty} from 'lodash';
import {useState} from 'react';
import {useTranslation} from 'react-i18next';
import {FlatList, RefreshControl, Text, View} from 'react-native';

export default function () {
  const {t} = useTranslation();
  const query: [{[key: string]: string | number}] | any = useLocalSearchParams<{
    query?: string | [];
  }>();
  const [page, setPage] = useState<string | number>(query?.page ?? 1);
  const {
    data: elements,
    isFetching,
    isSuccess,
    refetch,
  } = useGetDocsQuery<{
    data: AppQueryResult<Doc[]>;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>({
    page,
    ...query,
  });

  if (isFetching) return <MainLoadingView />;

  return (
    <FlatList
      refreshControl={
        <RefreshControl
          refreshing={isFetching}
          onRefresh={refetch}
          tintColor={tw.color(`theme-700`)}
        />
      }
      style={tw`mx-3 rounded-xl `}
      contentContainerStyle={tw`flex w-full flex-grow android:pb-26 justify-start  self-center items-center gap-y-4 my-[2%] p-3 bg-white rounded-xl`}
      showsVerticalScrollIndicator={false}
      contentInset={tw.style(`ios:bottom-26`)}
      ListHeaderComponentStyle={tw`flex w-full`}
      ListFooterComponentStyle={tw`flex relative bottom-0 w-full flex-row-reverse justify-center items-center my-2 mx-10 p-3`}
      keyExtractor={(item: any) => item.id}
      ListHeaderComponent={
        <View style={tw`flex flex-col my-2`}>
          {!isEmpty(elements?.data) && (
            <TagSection query={{group: 'document'}} />
          )}
          <View style={tw`w-full pt-2`}>
            <Text
              style={tw`text-lg font-alfont capitalize text-prim-800 text-left`}>
              {t('documents')}
            </Text>
          </View>
        </View>
      }
      data={elements?.data}
      renderItem={({item}: {item: Doc}) => (
        <DocumentCard element={item} width={`ios:w-[100%] android:w-90`} />
      )}
      ListEmptyComponent={<NoResults />}
      ListFooterComponent={() =>
        !isEmpty(elements) &&
        elements.meta && (
          <Pagination
            currentPage={elements.meta.current_page}
            lastPage={elements.meta.last_page}
            page={page}
            setPage={setPage}
          />
        )
      }
    />
  );
}
