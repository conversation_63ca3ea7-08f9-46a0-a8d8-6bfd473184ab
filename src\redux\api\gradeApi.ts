import {AppQueryResult, Grade} from '@root/src/types';
import {apiSlice} from '@src/redux/api/apiSlice';

export const gradeApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getGrades: builder.query<
      AppQueryResult<Grade[]>,
      {[key: string]: string | number} | void
    >({
      query: params => {
        return {
          url: `grade`,
          params: {...params},
        };
      },
    }),
  }),
});

export const {useGetGradesQuery} = gradeApi;
