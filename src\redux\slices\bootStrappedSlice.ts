import {createSlice} from '@reduxjs/toolkit';

type Props = {
  isLoading: boolean;
};

const initialState: Props = {
  isLoading: true,
};
export const bootStrappedSlice = createSlice({
  name: 'bootStrapped',
  initialState,
  reducers: {
    enableIsLoading: () => {
      return {
        isLoading: true,
      };
    },
    disableIsLoading: () => {
      return {
        isLoading: false,
      };
    },
    resetApp: (state: typeof initialState) => state,
  },
});

export const {enableIsLoading, disableIsLoading, resetApp} =
  bootStrappedSlice.actions;
