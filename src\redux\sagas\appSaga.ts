import {DrawerActions} from '@react-navigation/native';
import {PayloadAction} from '@reduxjs/toolkit';
import {disableIsLoading} from '@root/src/redux/slices/bootStrappedSlice';
import {I18nManager} from 'react-native';
import {delay, put} from 'redux-saga/effects';
import {PURGE} from 'redux-persist';

export function* startEnableIsLoadingScenario() {
  try {
    yield delay(5000);
    yield put(disableIsLoading());
  } catch (e) {
    console.error(e);
  }
}

export function* startDisableIsLoadingScenario() {
  // Your logic here
}

export function* startResetAppScenario() {
  // Dispatch PURGE action to reset the persisted state
  yield put({type: PURGE});
  //yield call(Updates.reloadAsync);    Uncomment If you want to test RTL
  //DevSettings.reload()
}

export function* startChangeLangScenario(_action: PayloadAction<string>) {
  try {
    //
    yield put(DrawerActions.closeDrawer());
    I18nManager.allowRTL(true);
    I18nManager.forceRTL(true);
    yield delay(100);
    //yield call(Updates.reloadAsync);  Uncomment If you want to test RTL
  } catch (e) {
    console.error(e);
  }
}
