{"expo": {"name": "StriveEdu", "slug": "strive-du", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "jsEngine": "hermes", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"ExpoLocalization_supportsRTL": true}, "bundleIdentifier": "com.pixeltechkw.strivedu", "buildNumber": "3"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/strive_logo.png", "backgroundColor": "#ffffff"}, "package": "com.pixeltechkw.strivedu"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "experiments": {"tsconfigPaths": true}, "extra": {"apiUrl": "https://strive-backend.pixelteckw.net/api/", "api_Key": "83487382947328974", "Test": "123", "supportsRTL": true, "router": {"origin": false}, "eas": {"projectId": "5b044fd6-bffe-49d6-a468-b68e7b00a24b"}}, "plugins": ["expo-router", ["expo-localization", {"interfaceLocalization": {"rtl": true}}], ["expo-updates", {"username": "uusa35", "email": "<EMAIL>"}], ["expo-font", {"fonts": ["assets/fonts/alfont_com_AlFont_com_ExpoArabic-SemiBold.ttf", "assets/fonts/ExpoArabic-Book.ttf", "assets/fonts/SuisseIntl-Medium.otf", "assets/fonts/SuisseIntl-SemiBold.otf"]}], ["expo-asset", {"assets": ["./assets/images/", "./assets/fonts/", "./assets/icons/"]}]], "owner": "kw-pixeltechnology", "runtimeVersion": "exposdk:49.0.0", "updates": {"url": "https://u.expo.dev/5b044fd6-bffe-49d6-a468-b68e7b00a24b"}}}