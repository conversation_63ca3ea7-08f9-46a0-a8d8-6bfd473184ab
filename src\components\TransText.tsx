import tw from '@root/tailwind';
import {FC} from 'react';
import {Text} from 'react-native';
import {useAppSelector} from '../redux/hooks';

type Props = {
  className: string;
  title: string;
  length?: number;
  lines?: number;
};
const TransText: FC<Props> = ({
  className,
  title,
  length = 99999,
  lines = 1,
}) => {
  const {isRTL} = useAppSelector(state => state.locale);

  return (
    <Text
      style={tw.style(` ${className}  capitalize font-alfont truncate`)}
      numberOfLines={lines}>
      {title}
    </Text>
  );
};

export default TransText;