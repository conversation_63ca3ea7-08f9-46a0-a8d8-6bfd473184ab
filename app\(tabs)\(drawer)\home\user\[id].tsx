import {AntDesign, Entypo} from '@expo/vector-icons';
import MainLoadingView from '@root/src/components/loading/MainLoadingView';
import {appRoutes, icons, imageSizes} from '@root/src/constants';
import {useGetUserQuery} from '@root/src/redux/api/userApi';
import {useAppSelector} from '@root/src/redux/hooks';
import {TeacherCourse, User} from '@root/src/types';
import tw from '@root/tailwind';
import {Image} from 'expo-image';

import {useLocalSearchParams, useNavigation, useRouter} from 'expo-router';
import {t} from 'i18next';
import {map, truncate} from 'lodash';
import {useState} from 'react';
import {Pressable, ScrollView, Text, View} from 'react-native';
import {List} from 'react-native-paper';

export default function () {
  const {id}: Partial<{id: string}> = useLocalSearchParams();
  const params = useLocalSearchParams();
  const [isActiveUser, setIsActiveUser] = useState(false);
  const router = useRouter();
  const {setOptions} = useNavigation();
  const {
    data: element,
    isFetching,
    isSuccess,
  } = useGetUserQuery<{
    data: User;
    isSuccess: boolean;
    isFetching: boolean;
    refetch: () => void;
  }>(id);

  const {
    locale: {isRTL},
  } = useAppSelector(state => state);

  if (!isSuccess || isFetching || !element) return <MainLoadingView />;

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-grow mx-auto w-full`}
      contentInset={{bottom: 100}}
      style={tw`rounded-xl m-3`}>
      {/* {User} */}
      <View style={tw`bg-gray-50 rounded-3xl p-4 my-2`}>
        <View style={tw`flex-row justify-start items-center gap-x-2 `}>
          <icons.instructor color={tw.color(``)} style={tw`h-10 w-10`} />
          <View style={tw`flex-col gap-x-2 `}>
            <Text
              style={tw`font-alfont text-sm capitalize text-black font-bold text-left`}>
              {element.name}
            </Text>
            <Text
              style={tw`font-alfont text-sm capitalize text-gray-400  text-left`}>
              {element.caption}
            </Text>
          </View>
        </View>
        <Text
          style={tw`font-alfont text-sm pt-4  px-2 capitalize text-gray-400  text-left`}>
          {truncate(element.description, {
            length: 55,
            omission: '...',
          })}
        </Text>
      </View>

      <View style={tw`bg-white my-2  rounded-3xl  p-3`}>
        {/* teacher_courses */}
        {element.teacher_courses.length !== 0 && (
          <View>
            <View style={tw`w-full rounded-3xl border border-gray-100 my-2`}>
              <List.Accordion
                title={t('courses')}
                onPress={() => setIsActiveUser(!isActiveUser)}
                left={() => <></>}
                expanded={!isActiveUser} // Set to true to collapse the accordion by default
                right={() => (
                  <View style={tw`flex-row justify-start items-center gap-x-2`}>
                    <Text
                      style={tw`text-xs font-alfont capitalize text-gray-500`}>
                      {`${t('courses')} (${element.teacher_courses.length})`}
                    </Text>
                    <View style={tw`mx-2`}>
                      <Entypo
                        size={imageSizes.xxs}
                        color={tw.color(`text-gray-500`)}
                        name={isActiveUser ? 'chevron-up' : 'chevron-down'}
                      />
                    </View>
                  </View>
                )}
                rippleColor={tw.color('white')}
                titleStyle={tw`font-alfont h-auto  flex  justify-center`}
                style={tw`flex flex-row justify-between items-center self-center  w-full my-0 py-0 h-10 border-b border-gray-100 bg-gray-50 rounded-t-3xl border-white`}>
                {map(element.teacher_courses, (s: TeacherCourse, i: number) => (
                  <Pressable
                    onPress={() =>
                      router.push(
                        appRoutes(`${s.id}?name=${s.name}&order_paid=0`)
                          .courseShow,
                      )
                    }
                    key={s.id}
                    style={tw`px-1 py-3 flex-row justify-center items-center self-center w-full h-15 ${
                      i + 1 === element.teacher_courses.length ? `` : `border-b`
                    } border-gray-100`}>
                    <View
                      style={tw`flex-row p-1.5 flex-1 w-[95%] justify-center items-center `}>
                      <View style={tw`px-0.5 items-center`}>
                        <Image
                          source={s.thumb}
                          style={tw`h-10 w-10  rounded-xl border border-gray-100 text-gray-400`}
                          contentFit="cover"
                        />
                      </View>
                      <View style={tw`flex-1 px-2 gap-y-2`}>
                        <Text
                          style={tw`font-alfont text-sm text-black font-bold capitalize text-left `}>
                          {truncate(s.name, {
                            length: 35,
                            omission: '...',
                          })}
                        </Text>

                        <Text
                          style={tw`font-alfont text-xs text-gray-500 capitalize text-left `}>
                          {truncate(s.description, {
                            length: 35,
                            omission: '...',
                          })}
                        </Text>
                      </View>
                      <View style={tw`items-center`}>
                        <Entypo
                          name={`chevron-thin-${isRTL ? 'left' : 'right'}`}
                          size={imageSizes.xxs}
                          color={tw.color(`text-gray-500`)}
                        />
                      </View>
                    </View>
                  </Pressable>
                ))}
              </List.Accordion>
            </View>
          </View>
        )}
      </View>
    </ScrollView>
  );
}
