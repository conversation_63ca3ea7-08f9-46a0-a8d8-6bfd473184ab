import {AppQueryResult, Auth, Invoice, Order} from '@root/src/types';
import {apiSlice} from '@src/redux/api/apiSlice';

export const authApi = apiSlice.injectEndpoints({
  endpoints: builder => ({
    postAuth: builder.mutation<
      Auth,
      {login: string; password: string; remember: boolean}
    >({
      query: body => ({
        url: `login`,
        method: 'POST',
        body,
      }),
    }),
    register: builder.mutation<
      Auth,
      {
        username: string;
        email: string;
        password: string;
        password_confirmation: string;
        mobile: string;
        school_name?: string;
        grade_id: number;
      }
    >({
      query: body => ({url: `register`, method: 'POST', body}),
    }),
    update: builder.mutation<
      Auth,
      {
        id: string;
        username: string;
        email: string;
        mobile: string;
        school_name?: string;
        gender?: string;
        grade_id: number;
      }
    >({
      query: body => ({url: `profile/user/${body.id}`, method: 'PUT', body}),
    }),
    deleteUser: builder.mutation<void, string | number | undefined>({
      query: id => ({url: `profile/user/${id}`, method: 'Delete'}),
    }),
    createOrder: builder.mutation<Invoice, any>({
      query: body => ({url: `order`, method: 'POST', body}),
    }),
    updateOrder: builder.mutation<any, any>({
      query: body => ({url: `order/${body.id}`, method: 'PUT', body}),
    }),
    getMyInvoices: builder.query<AppQueryResult<Order[]>, void>({
      query: body => ({
        url: `profile/invoices`,
        method: 'POST',
      }),
    }),
    getInvoice: builder.query<Order, number | string>({
      query: id => ({
        url: `profile/invoice/${id}`,
        method: 'POST',
      }),
    }),
    getMyCourses: builder.query<Auth, void>({
      query: body => ({
        url: `profile/courses`,
        method: 'POST',
      }),
    }),
    getMyDocuments: builder.query<Auth, void>({
      query: body => ({
        url: `profile/documents`,
        method: 'POST',
      }),
    }),
    getMyQuestionnaires: builder.query<Auth, void>({
      query: body => ({
        url: `profile/questionnaires`,
        method: 'POST',
      }),
    }),
    getResult: builder.query<Auth, string | number>({
      query: id => ({
        url: `profile/result/${id}`,
        method: 'POST',
      }),
    }),
    getCoupon: builder.query<any, string>({
      query: coupon => ({
        url: `coupon`,
        params: {coupon},
      }),
    }),
  }),
});

export const {
  usePostAuthMutation,
  useRegisterMutation,
  useUpdateMutation,
  useGetMyInvoicesQuery,
  useGetInvoiceQuery,
  useGetMyCoursesQuery,
  useLazyGetMyCoursesQuery,
  useGetMyDocumentsQuery,
  useLazyGetMyDocumentsQuery,
  useGetMyQuestionnairesQuery,
  useGetResultQuery,
  useCreateOrderMutation,
  useUpdateOrderMutation,
  useLazyGetCouponQuery,
  useDeleteUserMutation,
} = authApi;
