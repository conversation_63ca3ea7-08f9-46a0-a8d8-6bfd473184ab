module.exports = function (api) {
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      [
        'babel-plugin-module-resolver',
        {
          extensions: ['.ts', '.tsx', '.js', '.ios.js', '.android.js'],
          alias: {
            '@root': '.',
            '@app': './app/',
            '@app_types': './app/types/',
            '@app_api': './app/redux/api/',
            '@app_slices': './app/redux/slices/',
            '@app_redux': './app/redux',
            '@app_components': './components',
          },
        },
      ],
      'react-native-reanimated/plugin',
      'jest-hoist', // Assuming you need it for Jest
    ],
  };
};
