import {useAppSelector} from '@root/src/redux/hooks';
import tw from '@root/tailwind';
import {images} from '@src/constants';
import {Image} from 'expo-image';
import LottieView from 'lottie-react-native';
import React, {FC, useRef} from 'react';
import {Text, View} from 'react-native';

type Props = {
  message?: string;
};
const MainLoadingView: FC<Props> = ({message}): JSX.Element => {
  const {setting} = useAppSelector(state => state.appSetting);
  const animation = useRef<LottieView>(null);
  return (
    <View style={tw`flex flex-1 w-auto justify-center  items-center`}>
      <Image
        source={images.logo}
        style={tw`w-30 h-20`}
        contentFit={`contain`}
      />
      <LottieView
        ref={animation}
        style={tw`self-center h-30 w-30`}
        source={images.loader}
        autoPlay
        loop
      />
      {message && (
        <Text
          style={tw`text-md text-center text-black font-alfont  capitalize   pt-4 shadow-lg `}>
          {`${message}`}
        </Text>
      )}
    </View>
  );
};

export default MainLoadingView;
