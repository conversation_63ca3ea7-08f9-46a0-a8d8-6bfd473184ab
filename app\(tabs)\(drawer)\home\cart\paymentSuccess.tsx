import {images} from '@root/src/constants';
import {useAppDispatch} from '@root/src/redux/hooks';
import {resetCart} from '@root/src/redux/slices/cartSlice';
import tw from '@root/tailwind';
import {Image} from 'expo-image';
import {useEffect} from 'react';
import {useTranslation} from 'react-i18next';
import {ScrollView, Text, View} from 'react-native';
import {
  useLazyGetMyCoursesQuery,
  useLazyGetMyDocumentsQuery,
} from '@root/src/redux/api/authApi';

export default function () {
  const dispatch = useAppDispatch();
  const {t} = useTranslation();
  const [triggerGetMyCourses] = useLazyGetMyCoursesQuery();
  const [triggerGetMyDocuments] = useLazyGetMyDocumentsQuery();
  // const params: any = useLocalSearchParams();
  useEffect(() => {
    dispatch(resetCart());
    triggerGetMyCourses();
    triggerGetMyDocuments();
  }, []);
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={tw`flex flex-col bg-white rounded-3xl p-3`}
      contentInset={{bottom: 300}}
      style={tw`rounded-xl m-3`}>
      <View style={tw`flex-row justify-center items-center pt-3 p-3 gap-x-1`}>
        <Text
          style={tw`font-alfont text-center text-lg capitalize text-black font-bold `}>
          {t('payment_success')}
        </Text>
      </View>
      <View style={tw`flex-row justify-center items-center pt-3 p-3 gap-x-1`}>
        <Image
          source={images.payment_success}
          style={tw`h-100 w-100  rounded-xl border border-gray-100 text-gray-400`}
          contentFit="cover"
        />
      </View>
    </ScrollView>
  );
}
